# 智能健身教练后端服务

## 项目概述
这是智能健身教练小程序的后端服务，基于FastAPI框架开发，提供RESTful API接口。系统包含用户管理、健身数据跟踪、游戏化激励系统、AI智能助手和管理后台等功能，已实现用户登录验证、个人资料导入和修改等核心功能。应用采用像素风格，通过游戏化机制提高用户参与度和长期留存率。

## 技术栈
- Python 3.11
- FastAPI
- PostgreSQL
- Redis
- SQLAlchemy
- Alembic
- LangChain & LangGraph (AI对话系统)
- Jinja2 (管理后台页面渲染)
- Docker

## 项目结构
```
backend/
├── app/
│   ├── api/
│   │   ├── admin/         # 管理后台API
│   │   ├── endpoints/     # 主要API端点
│   │   └── v1/            # v1版本API路由
│   ├── core/              # 核心配置 (settings, security)
│   ├── crud/              # CRUD数据库操作
│   ├── data/              # 初始/示例数据
│   ├── db/                # 数据库会话和基类
│   ├── models/            # SQLAlchemy数据模型
│   ├── schemas/           # Pydantic数据验证模式
│   ├── scripts/           # 数据库脚本等
│   ├── services/          # 业务逻辑服务
│   │   ├── conversation/  # 对话系统服务
│   │   ├── graph_nodes/   # LangGraph节点
│   │   └── llm_proxy/     # LLM代理服务
│   ├── static/            # 应用内静态文件 (例如二维码html)
│   ├── templates/         # Jinja2模板 (主要是管理后台)
│   ├── utils/             # 工具函数
│   ├── avatar_converter.py # 头像转换工具
│   ├── main.py            # FastAPI应用入口
│   └── __init__.py
├── static/                # 全局静态资源文件 (由Nginx服务)
├── tests/                 # 测试代码
├── alembic/               # 数据库迁移
├── docs/                  # 详细文档
│   ├── user.md            # 用户相关功能文档
│   ├── auth.md            # 认证相关功能文档
│   ├── admin.md           # 管理后台文档
│   ├── api.md             # 详细API文档
│   ├── database.md        # 数据库管理文档
│   ├── deployment.md      # 详细部署指南
│   ├── exercise.md        # 健身动作模块文档
│   ├── gamification.md    # 游戏化系统文档
│   └── agent/             # AI助手相关文档
├── .env                   # 环境变量
├── docker-compose.yml     # Docker配置
├── Dockerfile             # Docker构建文件
└── requirements.txt       # 依赖包
```

## 核心功能模块

### 用户管理
- 用户注册与登录（微信小程序授权）
- 用户资料管理（基本信息、身体数据）
- 用户头像上传与处理
- 用户设置管理
- 详细文档：[用户模块文档](docs/user.md)

### 认证系统
- JWT令牌认证
- 令牌刷新机制
- 微信登录集成
- 用户会话管理
- 详细文档：[认证系统文档](docs/auth.md)

### 健身动作库
- 健身动作基础信息管理
- 动作详细信息与教学指导
- 根据身体部位、器材和肌肉分类查询
- 动作相关图片、GIF和视频资源
- 动作热度统计与排序
- 动作搜索功能
- 详细文档：[健身动作文档](docs/exercise.md)

### 训练计划
- 个性化训练计划生成
- 训练计划管理（创建、修改、删除）
- 训练记录与进度追踪
- 训练效果分析
- 详细文档：[训练计划文档](docs/training_plan.md)

### 饮食管理
- 食物库与营养数据
- 饮食记录与追踪
- AI食物识别
- 营养摄入分析与建议
- 详细文档：[饮食管理文档](docs/food.md)

### AI智能助手
- 基于LangChain和LangGraph的对话系统
- 个性化健身建议
- 训练动作和饮食指导
- 健康目标设定与追踪
- 详细文档：[AI助手文档](docs/agent/agent.md)

### 社区与团队
- 用户社区互动
- 团队创建与管理
- 团队训练与挑战
- 社交分享与动态
- 详细文档：[社区文档](docs/community.md)

### 游戏化系统（规划中）
- 用户等级与成长路线：通过健身活动获取经验值升级，解锁新装备和技能
- 卡片与储物系统：记录饮食和健身活动生成像素卡片，可收藏、合成和使用
- 虚拟货币系统：通过完成任务获取"像素杠铃"，可用于购买道具和装备
- 成就系统：奖励用户达成的里程碑，提供徽章、头像框等专属奖励
- 任务系统：包含日常任务、周常任务和挑战任务，设定阶段性目标
- PVP与排行榜：支持用户间挑战和排名，提供全局和好友排行榜
- 好友互动：支持组队训练、礼物赠送和社区互动功能
- 详细文档：[游戏化系统文档](docs/gamification.md)

### 小程序码生成
- 动态生成分享小程序码
- 分享追踪统计
- 详细文档：[小程序码文档](docs/qrcode.md)

### 管理后台
- 用户数据概览
- 用户管理功能
- 分享数据统计
- 内容管理
- 详细文档：[管理后台文档](docs/admin.md)

## 核心数据模型 (SQLAlchemy Models)

### 用户相关模型
- **用户 (User):** 核心用户信息（微信openid, 昵称, 头像, 基本身体数据等）
- **用户设置 (UserSetting):** 用户特定配置，如通知开关
- **用户统计 (UserStats):** 用户活动统计数据

### 健身动作相关模型
- **健身动作 (Exercise):** 动作基本信息（名称, 部位, 器材, 图片/GIF链接, 热度等）
- **动作详情 (ExerciseDetail):** 动作的详细指导（目标肌肉, 教学步骤, 视频等）
- **肌肉 (Muscle):** 肌肉信息
- **身体部位 (BodyPart):** 身体部位信息
- **器材 (Equipment):** 健身器材信息

### 训练计划相关模型
- **训练计划 (TrainingPlan):** 用户训练计划信息
- **训练模板 (TrainingTemplate):** 预设训练计划模板
- **日常训练 (DailyWorkout):** 单日训练内容
- **训练记录 (Workout):** 训练执行记录
- **训练动作 (WorkoutExercise):** 训练中的具体动作
- **组记录 (SetRecord):** 每组训练的详细记录

### 饮食相关模型
- **食物 (Food):** 食物基础信息（名称, 分类, 图片等）
- **营养概况 (NutritionalProfile):** 食物的主要营养成分概览（热量, 三大营养素比例等）
- **食物营养素 (FoodNutrientValue):** 食物详细营养素含量
- **食物单位 (FoodUnit):** 食物的计量单位（如克, 份）
- **餐食记录 (MealRecord):** 用户记录的餐食（日期, 类型, 图片, 总营养等）
- **食物项 (FoodItem):** 餐食记录中的具体食物项（名称, 重量, 营养详情）
- **食物项营养摄入 (FoodItemNutrientIntake):** 食物项的详细营养素摄入记录
- **健康建议 (HealthRecommendation):** 基于餐食记录生成的健康建议
- **食物识别 (FoodRecognition):** AI食物识别任务记录（图片, 状态, 识别结果）

### 社区与团队相关模型
- **团队 (Team):** 用户组成的训练团队
- **团队成员 (TeamMember):** 团队成员关系
- **社区动态 (CommunityPost):** 用户社区分享内容
- **点赞 (Like):** 内容点赞记录
- **评论 (Comment):** 用户评论

### AI助手相关模型
- **对话 (Conversation):** 用户与AI的对话会话
- **消息 (Message):** 对话中的具体消息
- **问答对 (QAPair):** 常见问答对，用于知识库

### 其他模型
- **用户收藏 (UserFavoriteExercise, UserFavoriteFood):** 用户收藏的动作和食物
- **分享追踪 (ShareTrack):** 小程序码分享和扫码追踪记录
- **反馈 (Feedback):** 用户反馈记录

### 游戏化系统模型（规划中）
- **用户等级 (UserLevel):** 用户经验值、等级和属性数据（体力、力量、敏捷、技能）
- **用户卡片 (UserCard):** 用户拥有的食物卡片和装备卡片
- **虚拟货币 (VirtualCurrency):** 用户"像素杠铃"数量和交易记录
- **成就记录 (Achievement):** 系统成就定义和用户已解锁成就
- **任务系统 (Task):** 日常、周常和挑战任务定义和完成状态
- **排行榜 (Leaderboard):** 用户在不同指标上的排名数据
- **用户挑战 (Challenge):** PVP挑战记录和结果
- **好友互动 (SocialInteraction):** 礼物赠送和社交互动记录

## 主要API接口

API接口基于FastAPI Router组织，主要模块如下：

### 认证相关接口 (auth.py)
- 微信登录/注册
- JWT令牌刷新
- 用户登出
- 微信手机号绑定
- 详细文档：[认证API文档](docs/auth.md)

### 用户相关接口 (user.py)
- 用户资料获取/更新
- 用户头像上传
- 用户设置管理
- 用户存在性检查
- 详细文档：[用户API文档](docs/user.md)

### 健身动作接口 (exercise.py)
- 动作列表获取与筛选
- 动作搜索
- 动作详情获取
- 动作媒体资源获取
- 详细文档：[健身动作API文档](docs/exercise.md)

### 训练计划接口 (training_plan.py, workout.py)
- 训练计划创建/获取/更新/删除
- 训练记录管理
- 训练进度追踪
- 训练数据统计
- 详细文档：[训练计划API文档](docs/training_plan.md)

### 饮食管理接口 (food.py, meal.py, food_recognition.py)
- 食物库查询
- 餐食记录管理
- 食物识别
- 营养分析
- 详细文档：[饮食API文档](docs/food.md)

### AI助手接口 (chat.py, ai_chat.py)
- 对话会话管理
- 消息发送和接收
- 个性化建议
- 健身/饮食指导
- 详细文档：[AI助手API文档](docs/agent/agent_flow.md)

### 社区与团队接口 (community.py, team.py)
- 社区动态发布/查询
- 点赞和评论
- 团队创建与管理
- 团队成员操作
- 详细文档：[社区API文档](docs/community.md)

### 游戏化系统API（规划中）
- 用户等级与属性相关接口
- 卡片管理相关接口
- 虚拟货币相关接口
- 成就系统相关接口
- 任务系统相关接口
- 排行榜相关接口
- 挑战相关接口
- 好友互动相关接口
- 详细文档：[游戏化系统API文档](docs/gamification.md)

### 其他接口
- 小程序码生成与管理 (qrcode.py)
- 分享追踪 (share.py)
- 健康检查 (health.py)
- LLM日志管理 (llm_logs.py)

完整API文档请访问部署后的 `/api/v1/docs` 或 `/api/v1/redoc`。

## 快速开始

### 环境要求
- Python 3.11+
- Docker & Docker Compose
- PostgreSQL 15
- Redis 7

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd backend
```

2. 创建虚拟环境并安装依赖
```bash
python -m venv venv
source .venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，设置必要的环境变量
```

4. 启动服务
```bash
killall uvicorn
pkill -f "uvicorn app.main:app" || true
sleep 2
source .venv/bin/activate  
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload > uvicorn.log 2>&1
```

### Docker部署

```bash
./docker-compose-v2 up -d
docker-compose up -d
```

更多详细的部署和开发说明，请参考 [部署文档](docs/deployment.md) 和 [开发指南](docs/development.md)。

## 参数定义中心

为了解决健身AI助手对话系统中的参数提取、收集和一致性问题，我们实现了统一的参数定义中心。该中心集中管理所有与训练、健身相关的参数定义，避免重复定义和不一致问题。

### 主要功能

1. **统一常量定义**：集中定义所有与训练和健身相关的常量，如身体部位、器材、肌肉分类等
2. **参数收集规则**：统一定义不同意图所需的参数及其收集顺序
3. **辅助函数集合**：提供参数提取、验证、格式化等辅助函数
4. **模板系统**：提供参数提问和确认的模板化支持

### 相关文件

- `app/core/param_definitions.py` - 参数定义中心
- `docs/param_definitions.md` - 使用文档
- `docs/param_definitions_example.py` - 使用示例

详细信息请参阅 [参数定义中心文档](docs/param_definitions.md)。

## 贡献指南
1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

详细的贡献流程请参考 [开发指南](docs/development.md)。

## 许可证
MIT License