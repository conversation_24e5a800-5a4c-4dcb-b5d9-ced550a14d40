#!/usr/bin/env python3
"""
API端点测试脚本
测试智能健身AI助手的主要API功能
"""

import requests
import json
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

def test_health_check():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 健康检查接口正常")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_home_page():
    """测试首页接口"""
    print("🔍 测试首页接口...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 首页接口正常: {data.get('message', '')}")
            return True
        else:
            print(f"❌ 首页接口失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 首页接口异常: {e}")
        return False

def test_api_docs():
    """测试API文档接口"""
    print("🔍 测试API文档接口...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/docs")
        if response.status_code == 200:
            print("✅ API文档接口正常")
            return True
        else:
            print(f"❌ API文档接口失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API文档接口异常: {e}")
        return False

def test_v2_endpoints_without_auth():
    """测试v2端点（无认证）"""
    print("🔍 测试v2端点（无认证）...")
    
    endpoints = [
        "/api/v2/chat/conversations",
        "/api/v2/chat/recent-messages",
        "/api/v2/chat/recent-conversations"
    ]
    
    results = []
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            if response.status_code == 401:
                print(f"✅ {endpoint} - 正确返回401未授权")
                results.append(True)
            else:
                print(f"❌ {endpoint} - 状态码: {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"❌ {endpoint} - 异常: {e}")
            results.append(False)
    
    return all(results)

def test_v2_stream_endpoints():
    """测试v2流式端点（无认证）"""
    print("🔍 测试v2流式端点（无认证）...")
    
    session_id = "test_session_123"
    endpoints = [
        f"/api/v2/chat/stream/{session_id}/connect",
        f"/api/v2/chat/stream/{session_id}",
        f"/api/v2/chat/poll/{session_id}"
    ]
    
    results = []
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            if response.status_code == 401:
                print(f"✅ {endpoint} - 正确返回401未授权")
                results.append(True)
            else:
                print(f"❌ {endpoint} - 状态码: {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"❌ {endpoint} - 异常: {e}")
            results.append(False)
    
    return all(results)

def test_server_status():
    """测试服务器整体状态"""
    print("🔍 测试服务器整体状态...")
    
    try:
        # 测试连接性
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务器连接失败: {response.status_code}")
            return False
        
        # 测试响应时间
        start_time = time.time()
        response = requests.get(f"{BASE_URL}/health")
        response_time = time.time() - start_time
        
        if response_time < 1.0:
            print(f"✅ 服务器响应时间正常: {response_time:.3f}秒")
        else:
            print(f"⚠️ 服务器响应较慢: {response_time:.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务器状态检查异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API端点测试...")
    print("=" * 50)
    
    tests = [
        ("服务器状态", test_server_status),
        ("健康检查", test_health_check),
        ("首页接口", test_home_page),
        ("API文档", test_api_docs),
        ("V2端点认证", test_v2_endpoints_without_auth),
        ("V2流式端点", test_v2_stream_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        result = test_func()
        results.append((test_name, result))
        time.sleep(0.5)  # 避免请求过快
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查系统状态。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 