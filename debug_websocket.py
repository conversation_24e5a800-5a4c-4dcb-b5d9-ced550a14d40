#!/usr/bin/env python3
"""
WebSocket流式响应调试脚本
"""

import asyncio
import websockets
import requests
import json
import time

BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000"

async def debug_websocket():
    """调试WebSocket流式响应"""
    
    # 1. 获取认证令牌
    print("🔐 获取认证令牌...")
    login_data = {
        "code": "test_code",
        "openid": "oCU0j7Rg9kzigLzquCBje3KfnQXk",
        "userInfo": {
            "nickname": "测试用户",
            "avatarUrl": "https://example.com/avatar.jpg"
        }
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login/wechat", json=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return
    
    token = response.json().get("token")
    if not token:
        print("❌ 未获取到令牌")
        return
    
    print(f"✅ 成功获取令牌: {token[:20]}...")
    
    # 2. 建立WebSocket连接
    session_id = f"debug_session_{int(time.time())}"
    ws_url = f"{WS_URL}/api/v2/chat/stream/{session_id}"
    headers = [("Authorization", f"Bearer {token}")]
    
    print(f"🔗 连接WebSocket: {ws_url}")
    
    try:
        async with websockets.connect(ws_url, additional_headers=headers) as websocket:
            print("✅ WebSocket连接成功")
            
            # 3. 等待连接确认
            welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            welcome_data = json.loads(welcome_msg)
            print(f"📨 连接确认: {welcome_data}")
            
            # 4. 发送测试消息
            test_message = {
                "message": "你好，我想了解健身建议",
                "meta_info": {
                    "user_profile": {
                        "age": 25,
                        "gender": "男",
                        "fitness_goal": "减脂"
                    }
                }
            }
            
            print(f"📤 发送消息: {test_message['message']}")
            await websocket.send(json.dumps(test_message))
            
            # 5. 接收所有响应
            response_count = 0
            start_time = time.time()
            
            print("📥 开始接收响应...")
            
            while time.time() - start_time < 20.0:  # 等待20秒
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    response_data = json.loads(response)
                    response_count += 1
                    
                    event_type = response_data.get("event", "unknown")
                    print(f"📨 响应 #{response_count}: 事件={event_type}")
                    print(f"    数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                    
                    if event_type == "done":
                        print("✅ 响应完成")
                        break
                    elif event_type == "error":
                        print("❌ 收到错误响应")
                        break
                        
                except asyncio.TimeoutError:
                    print("⏰ 2秒内未收到响应，继续等待...")
                    continue
            
            print(f"📊 总共收到 {response_count} 条响应")
            
    except Exception as e:
        print(f"❌ WebSocket连接异常: {e}")

if __name__ == "__main__":
    asyncio.run(debug_websocket()) 