#!/usr/bin/env python3
"""
V2流式端点全面测试脚本
基于ai_assistant_testing.md中的测试方案，模拟真实微信用户场景
"""

import asyncio
import websockets
import requests
import json
import time
import uuid
from typing import Dict, Any, Optional
from dataclasses import dataclass

BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000"

@dataclass
class TestUser:
    """测试用户数据类"""
    name: str
    age: int
    gender: str
    height: float
    weight: float
    fitness_goal: str
    experience_level: str
    activity_level: str
    description: str
    auth_token: Optional[str] = None

# 测试用户画像（基于ai_assistant_testing.md）
TEST_USERS = {
    "beginner": TestUser(
        name="王小明",
        age=25,
        gender="男",
        height=175.0,
        weight=80.0,
        fitness_goal="减脂",
        experience_level="初学者",
        activity_level="低",
        description="25岁男性，无健身经验，目标减脂",
        auth_token=None  # 需要用户提供
    ),
    "intermediate": TestUser(
        name="李晓华",
        age=30,
        gender="女",
        height=165.0,
        weight=55.0,
        fitness_goal="增肌",
        experience_level="中级",
        activity_level="中等",
        description="30岁女性，2年健身经验，目标增肌",
        auth_token=None  # 需要用户提供
    ),
    "advanced": TestUser(
        name="张教练",
        age=35,
        gender="男",
        height=180.0,
        weight=75.0,
        fitness_goal="备战比赛",
        experience_level="高级",
        activity_level="高",
        description="35岁男性，5年以上经验，备战比赛",
        auth_token=None  # 需要用户提供
    ),
    "special_needs": TestUser(
        name="刘阿姨",
        age=45,
        gender="女",
        height=160.0,
        weight=65.0,
        fitness_goal="康复",
        experience_level="初学者",
        activity_level="低",
        description="45岁女性，腰椎问题，目标康复",
        auth_token=None  # 需要用户提供
    )
}

class V2StreamingTester:
    """V2流式端点测试器"""
    
    def __init__(self, user: TestUser):
        self.user = user
        self.session_id = f"test_session_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        self.headers = {
            "Authorization": f"Bearer {user.auth_token}" if user.auth_token else "Bearer test_token",
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2503300 MicroMessenger/8.0.5"
        }
        self.test_results = []
    
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if details:
            print(f"    详情: {details}")
        self.test_results.append((test_name, success, details))
    
    def test_http_endpoints(self):
        """测试HTTP端点"""
        print(f"\n📋 测试用户 {self.user.name} 的HTTP端点:")
        
        # 测试会话列表
        try:
            response = requests.get(f"{BASE_URL}/api/v2/chat/conversations", headers=self.headers)
            if response.status_code in [200, 401]:  # 401表示认证失败，但端点正常
                self.log_test_result("会话列表接口", True, f"状态码: {response.status_code}")
            else:
                self.log_test_result("会话列表接口", False, f"意外状态码: {response.status_code}")
        except Exception as e:
            self.log_test_result("会话列表接口", False, f"异常: {e}")
        
        # 测试消息历史
        try:
            response = requests.get(f"{BASE_URL}/api/v2/chat/sessions/{self.session_id}/messages", headers=self.headers)
            if response.status_code in [200, 401, 404]:
                self.log_test_result("消息历史接口", True, f"状态码: {response.status_code}")
            else:
                self.log_test_result("消息历史接口", False, f"意外状态码: {response.status_code}")
        except Exception as e:
            self.log_test_result("消息历史接口", False, f"异常: {e}")
        
        # 测试轮询接口
        try:
            response = requests.get(f"{BASE_URL}/api/v2/chat/poll/{self.session_id}", headers=self.headers)
            if response.status_code in [200, 401, 404]:
                self.log_test_result("轮询接口", True, f"状态码: {response.status_code}")
            else:
                self.log_test_result("轮询接口", False, f"意外状态码: {response.status_code}")
        except Exception as e:
            self.log_test_result("轮询接口", False, f"异常: {e}")
        
        # 测试流式消息发送
        try:
            message_data = {
                "message": f"你好，我是{self.user.name}，我想开始健身",
                "meta_info": {
                    "user_profile": {
                        "age": self.user.age,
                        "gender": self.user.gender,
                        "fitness_goal": self.user.fitness_goal
                    }
                }
            }
            response = requests.post(
                f"{BASE_URL}/api/v2/chat/stream/{self.session_id}/message",
                headers=self.headers,
                json=message_data
            )
            if response.status_code in [200, 401]:
                self.log_test_result("流式消息发送", True, f"状态码: {response.status_code}")
            else:
                self.log_test_result("流式消息发送", False, f"意外状态码: {response.status_code}")
        except Exception as e:
            self.log_test_result("流式消息发送", False, f"异常: {e}")
        
        # 测试连接信息接口
        try:
            response = requests.get(f"{BASE_URL}/api/v2/chat/stream/{self.session_id}/connect", headers=self.headers)
            if response.status_code in [200, 401]:
                self.log_test_result("连接信息接口", True, f"状态码: {response.status_code}")
            else:
                self.log_test_result("连接信息接口", False, f"意外状态码: {response.status_code}")
        except Exception as e:
            self.log_test_result("连接信息接口", False, f"异常: {e}")
    
    async def test_websocket_connection(self):
        """测试WebSocket连接"""
        print(f"\n📋 测试用户 {self.user.name} 的WebSocket连接:")
        
        ws_url = f"{WS_URL}/api/v2/chat/stream/{self.session_id}"
        
        try:
            # 尝试连接WebSocket (修复extra_headers兼容性问题)
            headers = [("Authorization", self.headers["Authorization"])]
            async with websockets.connect(ws_url, additional_headers=headers) as websocket:
                self.log_test_result("WebSocket连接", True, "连接成功")
                
                # 等待连接确认消息
                try:
                    welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    welcome_data = json.loads(welcome_msg)
                    if welcome_data.get("event") == "connected":
                        self.log_test_result("连接确认消息", True, f"会话ID: {welcome_data.get('session_id')}")
                    else:
                        self.log_test_result("连接确认消息", False, f"意外消息: {welcome_data}")
                except asyncio.TimeoutError:
                    self.log_test_result("连接确认消息", False, "超时未收到确认消息")
                
                # 发送测试消息
                test_message = {
                    "message": f"我是{self.user.name}，{self.user.description}，请给我一些健身建议",
                    "meta_info": {
                        "user_type": self.user.experience_level,
                        "fitness_goal": self.user.fitness_goal
                    }
                }
                
                await websocket.send(json.dumps(test_message))
                self.log_test_result("发送测试消息", True, "消息已发送")
                
                # 接收响应
                response_count = 0
                start_time = time.time()
                
                try:
                    while time.time() - start_time < 10.0 and response_count < 5:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        response_data = json.loads(response)
                        response_count += 1
                        
                        event_type = response_data.get("event", "unknown")
                        if event_type == "heartbeat":
                            self.log_test_result(f"心跳消息 #{response_count}", True, "收到心跳")
                        elif event_type == "message_saved":
                            self.log_test_result("消息保存确认", True, f"消息ID: {response_data.get('message_id')}")
                        elif event_type == "chunk":
                            self.log_test_result(f"流式响应 #{response_count}", True, f"内容片段: {response_data.get('data', {}).get('text', '')[:20]}...")
                        elif event_type == "done":
                            self.log_test_result("响应完成", True, "AI回复完成")
                            break
                        elif event_type == "error":
                            self.log_test_result("错误响应", False, f"错误: {response_data.get('data', {}).get('message', '')}")
                        else:
                            self.log_test_result(f"其他响应 #{response_count}", True, f"事件: {event_type}")
                
                except asyncio.TimeoutError:
                    self.log_test_result("响应接收", False, "超时未收到完整响应")
                
                if response_count > 0:
                    self.log_test_result("WebSocket通信", True, f"收到 {response_count} 条响应")
                else:
                    self.log_test_result("WebSocket通信", False, "未收到任何响应")
        
        except Exception as e:
            self.log_test_result("WebSocket连接", False, f"连接异常: {e}")
    
    def test_conversation_scenarios(self):
        """测试对话场景"""
        print(f"\n📋 测试用户 {self.user.name} 的对话场景:")
        
        # 根据用户类型设计不同的测试场景
        scenarios = self._get_user_scenarios()
        
        for scenario_name, messages in scenarios.items():
            print(f"\n  🎭 场景: {scenario_name}")
            
            for i, message in enumerate(messages):
                try:
                    response = requests.post(
                        f"{BASE_URL}/api/v2/chat/stream/{self.session_id}/message",
                        headers=self.headers,
                        json={"message": message, "meta_info": {"scenario": scenario_name}}
                    )
                    
                    if response.status_code in [200, 401]:
                        self.log_test_result(f"消息 {i+1}", True, f"'{message[:30]}...' -> {response.status_code}")
                    else:
                        self.log_test_result(f"消息 {i+1}", False, f"状态码: {response.status_code}")
                        
                    time.sleep(0.5)  # 避免请求过快
                    
                except Exception as e:
                    self.log_test_result(f"消息 {i+1}", False, f"异常: {e}")
    
    def _get_user_scenarios(self) -> Dict[str, list]:
        """根据用户类型获取测试场景"""
        base_scenarios = {
            "基础问候": [
                f"你好，我是{self.user.name}",
                "我想开始健身",
                "你能帮助我吗？"
            ],
            "信息收集": [
                "我需要制定健身计划",
                f"我{self.user.age}岁，{self.user.gender}性",
                f"我的目标是{self.user.fitness_goal}"
            ]
        }
        
        # 根据用户类型添加特定场景
        if self.user.experience_level == "初学者":
            base_scenarios["新手指导"] = [
                "我完全没有健身经验",
                "应该从什么开始？",
                "有什么需要注意的安全事项吗？"
            ]
        elif self.user.experience_level == "中级":
            base_scenarios["进阶训练"] = [
                "我已经健身2年了",
                "想要更专业的训练计划",
                "如何突破平台期？"
            ]
        elif self.user.experience_level == "高级":
            base_scenarios["专业咨询"] = [
                "我是健身教练",
                "需要备战比赛的训练方案",
                "请给我详细的营养和训练建议"
            ]
        
        if "康复" in self.user.fitness_goal:
            base_scenarios["康复训练"] = [
                "我有腰椎问题",
                "需要安全的康复训练",
                "哪些动作我应该避免？"
            ]
        
        return base_scenarios
    
    def generate_summary(self):
        """生成测试总结"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for _, success, _ in self.test_results if success)
        
        print(f"\n📊 用户 {self.user.name} 测试总结:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过数: {passed_tests}")
        print(f"  失败数: {total_tests - passed_tests}")
        print(f"  通过率: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests < total_tests:
            print("\n❌ 失败的测试:")
            for test_name, success, details in self.test_results:
                if not success:
                    print(f"    - {test_name}: {details}")
        
        return passed_tests, total_tests

async def run_comprehensive_tests():
    """运行全面测试"""
    print("🚀 开始V2流式端点全面测试...")
    print("=" * 60)
    
    # 检查用户认证信息
    missing_tokens = []
    for user_type, user in TEST_USERS.items():
        if not user.auth_token or user.auth_token == "test_token":
            missing_tokens.append(f"{user_type} ({user.name})")
    
    if missing_tokens:
        print("⚠️ 以下用户缺少有效的认证令牌:")
        for user_info in missing_tokens:
            print(f"  - {user_info}")
        print("\n💡 将使用测试令牌进行基础功能测试...")
        print("   如需完整测试，请提供有效的用户认证令牌。")
        print("-" * 60)
    
    total_passed = 0
    total_tests = 0
    
    for user_type, user in TEST_USERS.items():
        print(f"\n👤 测试用户类型: {user_type.upper()}")
        print(f"   用户信息: {user.description}")
        print("-" * 40)
        
        tester = V2StreamingTester(user)
        
        # HTTP端点测试
        tester.test_http_endpoints()
        
        # WebSocket测试
        try:
            await tester.test_websocket_connection()
        except Exception as e:
            print(f"❌ WebSocket测试异常: {e}")
        
        # 对话场景测试
        tester.test_conversation_scenarios()
        
        # 生成用户测试总结
        passed, total = tester.generate_summary()
        total_passed += passed
        total_tests += total
        
        print("\n" + "=" * 60)
    
    # 总体测试结果
    print(f"\n🎯 总体测试结果:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过数: {total_passed}")
    print(f"  失败数: {total_tests - total_passed}")
    print(f"  总通过率: {total_passed/total_tests*100:.1f}%")
    
    if total_passed == total_tests:
        print("\n🎉 所有测试通过！V2流式端点功能正常。")
    else:
        print(f"\n⚠️ 有 {total_tests - total_passed} 个测试失败，请检查相关功能。")
    
    return total_passed == total_tests

def main():
    """主函数"""
    print("📋 V2流式端点全面测试")
    print("基于 ai_assistant_testing.md 测试方案")
    print("模拟真实微信用户场景")
    print("=" * 60)
    
    # 询问用户是否需要提供认证信息
    print("\n❓ 测试配置:")
    print("1. 使用测试令牌进行基础功能测试")
    print("2. 提供真实用户令牌进行完整测试")
    
    choice = input("\n请选择测试模式 (1/2): ").strip()
    
    if choice == "2":
        print("\n📝 请提供以下用户的认证令牌:")
        for user_type, user in TEST_USERS.items():
            token = input(f"  {user.name} ({user_type}): ").strip()
            if token:
                user.auth_token = token
    
    # 运行测试
    try:
        success = asyncio.run(run_comprehensive_tests())
        return success
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n\n❌ 测试执行异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 