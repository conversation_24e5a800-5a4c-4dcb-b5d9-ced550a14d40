from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.crud.base import CRUDBase
from app.models.community import (
    Post, Comment, PostLike, CommentLike, Report, PostReport, CommentReport, Notification
)
from app.schemas.community import (
    PostCreate, PostUpdate, CommentCreate, CommentUpdate,
    PostLikeCreate, CommentLikeCreate, ReportCreate, NotificationCreate
)


class CRUDPost(CRUDBase[Post, PostCreate, PostUpdate]):
    """帖子的CRUD操作"""
    
    def get_multi_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100, status: str = "approved"
    ) -> List[Post]:
        """获取用户的帖子列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数
            status: 帖子状态
            
        Returns:
            帖子列表
        """
        return db.query(Post).filter(
            Post.user_id == user_id,
            Post.status == status
        ).order_by(Post.created_at.desc()).offset(skip).limit(limit).all()
    
    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100, status: str = "approved"
    ) -> List[Post]:
        """获取帖子列表
        
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的记录数
            status: 帖子状态
            
        Returns:
            帖子列表
        """
        return db.query(Post).filter(
            Post.status == status
        ).order_by(Post.created_at.desc()).offset(skip).limit(limit).all()
    
    def get_total_count(self, db: Session, *, status: str = "approved") -> int:
        """获取帖子总数
        
        Args:
            db: 数据库会话
            status: 帖子状态
            
        Returns:
            帖子总数
        """
        return db.query(func.count(Post.id)).filter(Post.status == status).scalar()
    
    def get_by_user_count(self, db: Session, *, user_id: int, status: str = "approved") -> int:
        """获取用户的帖子总数
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            status: 帖子状态
            
        Returns:
            用户的帖子总数
        """
        return db.query(func.count(Post.id)).filter(
            Post.user_id == user_id,
            Post.status == status
        ).scalar()
    
    def increment_view_count(self, db: Session, *, id: int) -> Post:
        """增加帖子浏览次数
        
        Args:
            db: 数据库会话
            id: 帖子ID
            
        Returns:
            更新后的帖子
        """
        db_obj = db.query(Post).filter(Post.id == id).first()
        if db_obj:
            db_obj.view_count += 1
            db.commit()
            db.refresh(db_obj)
        return db_obj
    
    def update_status(self, db: Session, *, id: int, status: str) -> Post:
        """更新帖子状态
        
        Args:
            db: 数据库会话
            id: 帖子ID
            status: 新状态
            
        Returns:
            更新后的帖子
        """
        db_obj = db.query(Post).filter(Post.id == id).first()
        if db_obj:
            db_obj.status = status
            db.commit()
            db.refresh(db_obj)
        return db_obj
    
    def increment_report_count(self, db: Session, *, id: int) -> Post:
        """增加帖子举报次数
        
        Args:
            db: 数据库会话
            id: 帖子ID
            
        Returns:
            更新后的帖子
        """
        db_obj = db.query(Post).filter(Post.id == id).first()
        if db_obj:
            db_obj.reported_count += 1
            db.commit()
            db.refresh(db_obj)
        return db_obj


class CRUDComment(CRUDBase[Comment, CommentCreate, CommentUpdate]):
    """评论的CRUD操作"""
    
    def get_by_post(
        self, db: Session, *, post_id: int, skip: int = 0, limit: int = 100, status: str = "approved"
    ) -> List[Comment]:
        """获取帖子的评论列表
        
        Args:
            db: 数据库会话
            post_id: 帖子ID
            skip: 跳过的记录数
            limit: 返回的记录数
            status: 评论状态
            
        Returns:
            评论列表
        """
        return db.query(Comment).filter(
            Comment.post_id == post_id,
            Comment.parent_id == None,  # 只获取顶级评论
            Comment.status == status
        ).order_by(Comment.created_at.desc()).offset(skip).limit(limit).all()
    
    def get_replies(
        self, db: Session, *, comment_id: int, status: str = "approved"
    ) -> List[Comment]:
        """获取评论的回复列表
        
        Args:
            db: 数据库会话
            comment_id: 评论ID
            status: 评论状态
            
        Returns:
            回复列表
        """
        return db.query(Comment).filter(
            Comment.parent_id == comment_id,
            Comment.status == status
        ).order_by(Comment.created_at.asc()).all()
    
    def get_by_post_count(self, db: Session, *, post_id: int, status: str = "approved") -> int:
        """获取帖子的评论总数
        
        Args:
            db: 数据库会话
            post_id: 帖子ID
            status: 评论状态
            
        Returns:
            评论总数
        """
        return db.query(func.count(Comment.id)).filter(
            Comment.post_id == post_id,
            Comment.status == status
        ).scalar()
    
    def update_status(self, db: Session, *, id: int, status: str) -> Comment:
        """更新评论状态
        
        Args:
            db: 数据库会话
            id: 评论ID
            status: 新状态
            
        Returns:
            更新后的评论
        """
        db_obj = db.query(Comment).filter(Comment.id == id).first()
        if db_obj:
            db_obj.status = status
            db.commit()
            db.refresh(db_obj)
        return db_obj
    
    def increment_report_count(self, db: Session, *, id: int) -> Comment:
        """增加评论举报次数
        
        Args:
            db: 数据库会话
            id: 评论ID
            
        Returns:
            更新后的评论
        """
        db_obj = db.query(Comment).filter(Comment.id == id).first()
        if db_obj:
            db_obj.reported_count += 1
            db.commit()
            db.refresh(db_obj)
        return db_obj


class CRUDPostLike(CRUDBase[PostLike, PostLikeCreate, PostLikeCreate]):
    """帖子点赞的CRUD操作"""
    
    def get_by_user_and_post(self, db: Session, *, user_id: int, post_id: int) -> Optional[PostLike]:
        """获取用户对帖子的点赞
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            post_id: 帖子ID
            
        Returns:
            点赞记录
        """
        return db.query(PostLike).filter(
            PostLike.user_id == user_id,
            PostLike.post_id == post_id
        ).first()
    
    def create_like(self, db: Session, *, user_id: int, post_id: int) -> PostLike:
        """创建帖子点赞
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            post_id: 帖子ID
            
        Returns:
            创建的点赞记录
        """
        db_obj = PostLike(user_id=user_id, post_id=post_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def remove_like(self, db: Session, *, user_id: int, post_id: int) -> None:
        """删除帖子点赞
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            post_id: 帖子ID
        """
        db_obj = self.get_by_user_and_post(db=db, user_id=user_id, post_id=post_id)
        if db_obj:
            db.delete(db_obj)
            db.commit()
    
    def count_by_post(self, db: Session, *, post_id: int) -> int:
        """获取帖子的点赞数
        
        Args:
            db: 数据库会话
            post_id: 帖子ID
            
        Returns:
            点赞数
        """
        return db.query(func.count(PostLike.id)).filter(PostLike.post_id == post_id).scalar()
    
    def get_posts_liked_by_user(self, db: Session, *, user_id: int) -> List[int]:
        """获取用户点赞的帖子ID列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            帖子ID列表
        """
        likes = db.query(PostLike.post_id).filter(PostLike.user_id == user_id).all()
        return [like[0] for like in likes]


class CRUDCommentLike(CRUDBase[CommentLike, CommentLikeCreate, CommentLikeCreate]):
    """评论点赞的CRUD操作"""
    
    def get_by_user_and_comment(self, db: Session, *, user_id: int, comment_id: int) -> Optional[CommentLike]:
        """获取用户对评论的点赞
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            comment_id: 评论ID
            
        Returns:
            点赞记录
        """
        return db.query(CommentLike).filter(
            CommentLike.user_id == user_id,
            CommentLike.comment_id == comment_id
        ).first()
    
    def create_like(self, db: Session, *, user_id: int, comment_id: int) -> CommentLike:
        """创建评论点赞
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            comment_id: 评论ID
            
        Returns:
            创建的点赞记录
        """
        db_obj = CommentLike(user_id=user_id, comment_id=comment_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def remove_like(self, db: Session, *, user_id: int, comment_id: int) -> None:
        """删除评论点赞
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            comment_id: 评论ID
        """
        db_obj = self.get_by_user_and_comment(db=db, user_id=user_id, comment_id=comment_id)
        if db_obj:
            db.delete(db_obj)
            db.commit()
    
    def count_by_comment(self, db: Session, *, comment_id: int) -> int:
        """获取评论的点赞数
        
        Args:
            db: 数据库会话
            comment_id: 评论ID
            
        Returns:
            点赞数
        """
        return db.query(func.count(CommentLike.id)).filter(CommentLike.comment_id == comment_id).scalar()
    
    def get_comments_liked_by_user(self, db: Session, *, user_id: int) -> List[int]:
        """获取用户点赞的评论ID列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            评论ID列表
        """
        likes = db.query(CommentLike.comment_id).filter(CommentLike.user_id == user_id).all()
        return [like[0] for like in likes]


class CRUDReport(CRUDBase[Report, ReportCreate, ReportCreate]):
    """举报的CRUD操作"""
    
    def create_report(
        self, db: Session, *, reporter_id: int, reason: str, post_id: Optional[int] = None, comment_id: Optional[int] = None
    ) -> Report:
        """创建举报
        
        Args:
            db: 数据库会话
            reporter_id: 举报者ID
            reason: 举报原因
            post_id: 帖子ID
            comment_id: 评论ID
            
        Returns:
            创建的举报记录
        """
        db_obj = Report(
            reporter_id=reporter_id,
            reason=reason,
            post_id=post_id,
            comment_id=comment_id,
            status="pending"
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_pending_reports(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Report]:
        """获取待处理的举报列表
        
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的记录数
            
        Returns:
            举报列表
        """
        return db.query(Report).filter(Report.status == "pending").order_by(
            Report.created_at.desc()
        ).offset(skip).limit(limit).all()
    
    def resolve_report(self, db: Session, *, id: int, status: str) -> Report:
        """解决举报
        
        Args:
            db: 数据库会话
            id: 举报ID
            status: 新状态
            
        Returns:
            更新后的举报
        """
        db_obj = db.query(Report).filter(Report.id == id).first()
        if db_obj:
            db_obj.status = status
            db_obj.resolved_at = func.now()
            db.commit()
            db.refresh(db_obj)
        return db_obj


class CRUDNotification(CRUDBase[Notification, NotificationCreate, NotificationCreate]):
    """通知的CRUD操作"""
    
    def get_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[Notification]:
        """获取用户的通知列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数
            
        Returns:
            通知列表
        """
        return db.query(Notification).filter(Notification.user_id == user_id).order_by(
            Notification.created_at.desc()
        ).offset(skip).limit(limit).all()
    
    def get_unread_count(self, db: Session, *, user_id: int) -> int:
        """获取用户的未读通知数
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            未读通知数
        """
        return db.query(func.count(Notification.id)).filter(
            Notification.user_id == user_id,
            Notification.is_read == False
        ).scalar()
    
    def mark_as_read(self, db: Session, *, id: int) -> Notification:
        """将通知标记为已读
        
        Args:
            db: 数据库会话
            id: 通知ID
            
        Returns:
            更新后的通知
        """
        db_obj = db.query(Notification).filter(Notification.id == id).first()
        if db_obj:
            db_obj.is_read = True
            db.commit()
            db.refresh(db_obj)
        return db_obj
    
    def mark_all_as_read(self, db: Session, *, user_id: int) -> None:
        """将用户的所有通知标记为已读
        
        Args:
            db: 数据库会话
            user_id: 用户ID
        """
        db.query(Notification).filter(
            Notification.user_id == user_id,
            Notification.is_read == False
        ).update({"is_read": True})
        db.commit()
    
    def create_notification(
        self, db: Session, *, user_id: int, type: str, content: str,
        related_post_id: Optional[int] = None, related_comment_id: Optional[int] = None,
        related_user_id: Optional[int] = None
    ) -> Notification:
        """创建通知
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            type: 通知类型
            content: 通知内容
            related_post_id: 相关帖子ID
            related_comment_id: 相关评论ID
            related_user_id: 相关用户ID
            
        Returns:
            创建的通知
        """
        db_obj = Notification(
            user_id=user_id,
            type=type,
            content=content,
            related_post_id=related_post_id,
            related_comment_id=related_comment_id,
            related_user_id=related_user_id,
            is_read=False
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


post = CRUDPost(Post)
comment = CRUDComment(Comment)
post_like = CRUDPostLike(PostLike)
comment_like = CRUDCommentLike(CommentLike)
report = CRUDReport(Report)
notification = CRUDNotification(Notification)
