"""
LLM代理模块

该模块提供了与语言模型通信的代理服务，抽象了不同LLM提供商的API差异。
"""

import os
import json
import logging
import random
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union

from app.services.ai_assistant.common.cache import CacheService, default_cache_service

logger = logging.getLogger(__name__)


class LLMProxy(ABC):
    """
    LLM代理接口
    
    定义了与LLM通信的标准接口，包括生成文本、提取嵌入向量等功能。
    """
    
    @abstractmethod
    async def chat(
        self, 
        system_prompt: str, 
        user_message: str, 
        chat_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """
        进行对话，生成助手回复
        
        Args:
            system_prompt: 系统提示
            user_message: 用户消息
            chat_history: 聊天历史
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成token数
            model: 模型名称
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            助手回复文本
        """
        pass
    
    @abstractmethod
    async def generate_text(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """
        生成文本
        
        Args:
            prompt: 提示文本
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成token数
            model: 模型名称
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        pass
    
    @abstractmethod
    async def get_embeddings(
        self,
        texts: Union[str, List[str]],
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> List[List[float]]:
        """
        获取文本的嵌入向量
        
        Args:
            texts: 文本或文本列表
            model: 模型名称
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            嵌入向量列表
        """
        pass


class DefaultLLMProxy(LLMProxy):
    """
    默认LLM代理
    
    提供基本的LLM代理实现，用于测试和开发环境。
    实际应用中应替换为特定提供商的实现。
    """
    
    def __init__(self, cache_service: Optional[CacheService] = None):
        """
        初始化默认LLM代理
        
        Args:
            cache_service: 缓存服务，如果不提供则使用默认缓存服务
        """
        self.cache_service = cache_service or default_cache_service
    
    async def chat(
        self,
        system_prompt: str,
        user_message: str,
        chat_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """
        进行对话，生成助手回复
        
        Args:
            system_prompt: 系统提示
            user_message: 用户消息
            chat_history: 聊天历史
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成token数
            model: 模型名称
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            助手回复文本
        """
        logger.info(f"DefaultLLMProxy.chat被调用，用户消息: {user_message[:30]}...")
        
        # 如果使用缓存，尝试从缓存获取结果
        if use_cache and self.cache_service:
            # 构建缓存键
            cache_key = f"llm:chat:{system_prompt}:{user_message}"
            if chat_history:
                # 添加最近3轮对话历史到缓存键
                recent_history = chat_history[-3:] if len(chat_history) > 3 else chat_history
                history_str = json.dumps(recent_history, sort_keys=True)
                cache_key += f":{history_str}"
            
            cached_response = await self.cache_service.get(cache_key)
            if cached_response is not None:
                logger.debug(f"LLM响应缓存命中: {user_message[:30]}...")
                return cached_response
        
        # 使用预设的回复模板
        templates = [
            "这是一个模拟的AI助手回复。在实际应用中，这里将返回真实的LLM生成内容。您的消息是: {message}",
            "我是默认LLM代理，仅用于测试目的。您询问的是: {message}",
            "这是一个测试回复。在生产环境中，您将看到由实际LLM生成的回复。您的问题是关于: {message}"
        ]
        
        response = random.choice(templates).format(message=user_message[:50] + "..." if len(user_message) > 50 else user_message)
        
        # 如果使用缓存，将结果存入缓存
        if use_cache and self.cache_service:
            # 缓存1小时
            await self.cache_service.set(cache_key, response, ttl=3600)
        
        return response
    
    async def generate_text(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """
        生成文本
        
        Args:
            prompt: 提示文本
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成token数
            model: 模型名称
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        logger.info(f"DefaultLLMProxy.generate_text被调用，提示: {prompt[:30]}...")
        
        # 如果使用缓存，尝试从缓存获取结果
        if use_cache and self.cache_service:
            # 构建缓存键
            cache_key = f"llm:generate:{prompt}:{temperature}:{max_tokens}"
            
            cached_response = await self.cache_service.get(cache_key)
            if cached_response is not None:
                logger.debug(f"LLM生成缓存命中: {prompt[:30]}...")
                return cached_response
        
        # 生成模拟回复
        response = f"这是基于提示'{prompt[:30]}...'生成的模拟文本。在实际应用中，这将由真实的语言模型生成。"
        
        # 如果使用缓存，将结果存入缓存
        if use_cache and self.cache_service:
            # 缓存1小时
            await self.cache_service.set(cache_key, response, ttl=3600)
        
        return response
    
    async def get_embeddings(
        self,
        texts: Union[str, List[str]],
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> List[List[float]]:
        """
        获取文本的嵌入向量
        
        Args:
            texts: 文本或文本列表
            model: 模型名称
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            嵌入向量列表
        """
        # 确保texts是列表
        if isinstance(texts, str):
            texts = [texts]
        
        logger.info(f"DefaultLLMProxy.get_embeddings被调用，处理{len(texts)}个文本")
        
        results = []
        for text in texts:
            # 如果使用缓存，尝试从缓存获取结果
            embedding = None
            if use_cache and self.cache_service:
                # 构建缓存键
                cache_key = f"llm:embedding:{text}"
                
                cached_embedding = await self.cache_service.get(cache_key)
                if cached_embedding is not None:
                    logger.debug(f"嵌入缓存命中: {text[:30]}...")
                    embedding = cached_embedding
            
            # 如果缓存未命中，生成模拟嵌入
            if embedding is None:
                # 生成随机向量，维度为1536（OpenAI默认）
                embedding = [random.uniform(-1, 1) for _ in range(1536)]
                
                # 如果使用缓存，将结果存入缓存
                if use_cache and self.cache_service:
                    # 缓存1天
                    await self.cache_service.set(cache_key, embedding, ttl=86400)
            
            results.append(embedding)
        
        return results


class MockLLMProxy(LLMProxy):
    """
    模拟LLM代理
    
    用于测试的模拟LLM代理，返回固定响应。
    """
    
    async def chat(
        self, 
        system_prompt: str, 
        user_message: str, 
        chat_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """返回固定回复"""
        return f"这是模拟回复: {user_message[:20]}..."
    
    async def generate_text(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """返回固定生成文本"""
        return f"这是模拟生成文本: {prompt[:20]}..."
    
    async def get_embeddings(
        self,
        texts: Union[str, List[str]],
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> List[List[float]]:
        """返回固定嵌入向量"""
        # 确保texts是列表
        if isinstance(texts, str):
            texts = [texts]
        
        # 返回固定维度的随机向量
        return [[random.uniform(-1, 1) for _ in range(1536)] for _ in texts] 