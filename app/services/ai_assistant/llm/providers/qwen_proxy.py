"""
Qwen提供商LLM代理模块

该模块提供了与阿里云通义千问模型通信的代理服务。
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, Union

import aiohttp
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.common.cache import CacheService, default_cache_service
from app.core.chat_config import PROVIDERS
from app.core.config import settings

logger = logging.getLogger(__name__)

class QwenLLMProxy(LLMProxy):
    """
    通义千问LLM代理
    
    提供与阿里云通义千问API通信的代理实现。
    """
    
    def __init__(self, cache_service: Optional[CacheService] = None):
        """
        初始化通义千问LLM代理
        
        Args:
            cache_service: 缓存服务，如果不提供则使用默认缓存服务
        """
        self.cache_service = cache_service or default_cache_service
        self.api_key = settings.QWEN_API_KEY
        self.api_base = settings.QWEN_API_BASE
        self.default_model = "qwen-max"  # 默认使用qwen-max模型
        
        # 设置API配置
        self.provider_config = PROVIDERS.get("qwen", {})
        
        # 日志记录
        logger.info(f"QwenLLMProxy初始化完成，API基础URL: {self.api_base}")
    
    async def chat(
        self, 
        system_prompt: str, 
        user_message: str, 
        chat_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """
        进行对话，生成助手回复
        
        Args:
            system_prompt: 系统提示
            user_message: 用户消息
            chat_history: 聊天历史
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成token数
            model: 模型名称，默认使用self.default_model
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            助手回复文本
        """
        logger.info(f"QwenLLMProxy.chat被调用，用户消息: {user_message[:30]}...")
        
        # 如果使用缓存，尝试从缓存获取结果
        if use_cache and self.cache_service:
            # 构建缓存键
            cache_key = f"llm:qwen:chat:{system_prompt}:{user_message}"
            if chat_history:
                # 添加最近3轮对话历史到缓存键
                recent_history = chat_history[-3:] if len(chat_history) > 3 else chat_history
                history_str = json.dumps(recent_history, sort_keys=True)
                cache_key += f":{history_str}"
            
            cached_response = await self.cache_service.get(cache_key)
            if cached_response is not None:
                logger.debug(f"Qwen LLM响应缓存命中: {user_message[:30]}...")
                return cached_response
        
        # 使用指定的模型或默认模型
        model_name = model or self.default_model
        
        # 准备消息列表
        messages = []
        
        # 添加系统消息
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # 添加历史消息
        if chat_history:
            for msg in chat_history:
                role = msg.get("role", "user")
                content = msg.get("content", "")
                if role in ["user", "assistant", "system"]:
                    messages.append({"role": role, "content": content})
        
        # 添加当前用户消息
        messages.append({"role": "user", "content": user_message})
        
        # 准备请求数据
        request_data = {
            "model": model_name,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            **kwargs
        }
        
        try:
            # 发送请求到通义千问API
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}/chat/completions",
                    headers=headers,
                    json=request_data,
                    timeout=30  # 30秒超时
                ) as response:
                    response_data = await response.json()
                    
                    # 检查响应
                    if response.status != 200:
                        error_message = response_data.get("error", {}).get("message", "Unknown error")
                        logger.error(f"Qwen API错误: {error_message}")
                        return f"抱歉，通义千问API调用失败: {error_message}"
                    
                    # 解析响应
                    assistant_message = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                    
                    # 缓存结果
                    if use_cache and self.cache_service and assistant_message:
                        # 缓存1小时
                        await self.cache_service.set(cache_key, assistant_message, ttl=3600)
                    
                    return assistant_message
                    
        except Exception as e:
            logger.error(f"调用Qwen API时出错: {str(e)}")
            return f"抱歉，通义千问API调用时发生错误: {str(e)}"
    
    async def generate_text(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """
        生成文本
        
        Args:
            prompt: 提示文本
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成token数
            model: 模型名称，默认使用self.default_model
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        # 通过chat方法实现，将prompt作为用户消息
        logger.info(f"QwenLLMProxy.generate_text被调用，提示: {prompt[:30]}...")
        
        # 使用空的系统提示和历史
        return await self.chat(
            system_prompt="",
            user_message=prompt,
            chat_history=None,
            temperature=temperature,
            max_tokens=max_tokens,
            model=model,
            use_cache=use_cache,
            **kwargs
        )
    
    async def get_embeddings(
        self,
        texts: Union[str, List[str]],
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> List[List[float]]:
        """
        获取文本的嵌入向量
        
        Args:
            texts: 文本或文本列表
            model: 模型名称，默认使用嵌入模型
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            嵌入向量列表
        """
        # 确保texts是列表
        if isinstance(texts, str):
            texts = [texts]
        
        logger.info(f"QwenLLMProxy.get_embeddings被调用，处理{len(texts)}个文本")
        
        # 使用指定的模型或默认嵌入模型
        embedding_model = model or "text-embedding-v1"
        
        # 结果列表
        results = []
        
        for text in texts:
            # 如果使用缓存，尝试从缓存获取结果
            cache_key = None
            if use_cache and self.cache_service:
                cache_key = f"llm:qwen:embedding:{embedding_model}:{text}"
                cached_embedding = await self.cache_service.get(cache_key)
                if cached_embedding is not None:
                    logger.debug(f"Qwen嵌入缓存命中: {text[:30]}...")
                    results.append(cached_embedding)
                    continue
            
            try:
                # 准备请求数据
                request_data = {
                    "model": embedding_model,
                    "input": text,
                    **kwargs
                }
                
                # 发送请求到通义千问API
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.api_base}/embeddings",
                        headers=headers,
                        json=request_data,
                        timeout=30  # 30秒超时
                    ) as response:
                        response_data = await response.json()
                        
                        # 检查响应
                        if response.status != 200:
                            error_message = response_data.get("error", {}).get("message", "Unknown error")
                            logger.error(f"Qwen嵌入API错误: {error_message}")
                            # 使用零向量作为后备
                            embedding = [0.0] * 1536  # 标准维度
                        else:
                            # 解析响应
                            embedding = response_data.get("data", [{}])[0].get("embedding", [])
                        
                        # 添加到结果
                        results.append(embedding)
                        
                        # 缓存结果
                        if use_cache and self.cache_service and cache_key:
                            # 缓存24小时
                            await self.cache_service.set(cache_key, embedding, ttl=86400)
                
            except Exception as e:
                logger.error(f"调用Qwen嵌入API时出错: {str(e)}")
                # 使用零向量作为后备
                embedding = [0.0] * 1536  # 标准维度
                results.append(embedding)
        
        return results 