"""
健身建议状态模块

该模块实现了处理健身建议相关对话的状态。
"""

import logging
from typing import Dict, Any, Type, List, Optional

from app.services.ai_assistant.conversation.states.base import ConversationState
from app.services.ai_assistant.conversation.states.idle import IdleState
from app.services.ai_assistant.intent.handlers.factory import IntentHandlerFactory

logger = logging.getLogger(__name__)

class FitnessAdviceState(ConversationState):
    """
    健身建议状态
    
    处理与健身建议相关的对话，包括健身目标、训练频率、适合的运动等。
    """
    
    name = "fitness_advice"
    
    def __init__(self, context: Dict[str, Any]):
        """
        初始化健身建议状态
        
        Args:
            context: 对话上下文
        """
        super().__init__(context)
        self.intent_handler_factory = IntentHandlerFactory()
        
        # 获取健身建议处理器
        from app.services.ai_assistant.llm.proxy import DefaultLLMProxy
        from app.services.ai_assistant.knowledge.retriever import default_retriever
        
        llm_proxy = DefaultLLMProxy()
        self.fitness_advice_handler = self.intent_handler_factory.create(
            "fitness_advice",
            llm_proxy=llm_proxy,
            knowledge_retriever=default_retriever
        )
        
        # 状态控制
        self.need_transition = False
        self.next_state_name = None
        
        # 记录对话轮次
        self.turn_count = context.get("fitness_advice_turn_count", 0)
        self.context["fitness_advice_turn_count"] = self.turn_count
        
        # 最大对话轮次，超过后返回空闲状态
        self.max_turns = 5
    
    async def process_message(self, message: str) -> Dict[str, Any]:
        """
        处理用户消息
        
        处理健身建议相关的用户查询，并提供主动引导。
        
        Args:
            message: 用户输入的消息
            
        Returns:
            处理结果，包含响应和状态信息
        """
        logger.info(f"FitnessAdviceState处理消息: {message[:30]}...")
        
        # 增加对话轮次
        self.turn_count += 1
        self.context["fitness_advice_turn_count"] = self.turn_count
        
        # 检查是否超过最大轮次或用户想要结束
        if self.turn_count > self.max_turns or self._is_exit_intent(message):
            self.need_transition = True
            self.next_state_name = "idle"
            
            return {
                "response": "我已经为您提供了一些健身建议。还有其他健身相关问题吗？",
                "intent_type": "fitness_advice_complete",
                "confidence": 1.0,
                "transitioning": True,
                "next_state": "idle"
            }
        
        # 处理健身建议请求
        try:
            # 检查是否需要主动引导获取更多信息
            if self._needs_additional_info(message):
                proactive_response = self._generate_proactive_question(message)
                
                # 更新上下文
                self.update_context({
                    "previous_intent": "fitness_advice_proactive",
                    "previous_response": proactive_response,
                    "needs_info": self._identify_missing_info(message)
                })
                
                return {
                    "response": proactive_response,
                    "intent_type": "fitness_advice_proactive",
                    "confidence": 1.0,
                    "transitioning": False
                }
            
            # 标准健身建议处理
            handler_result = await self.fitness_advice_handler.handle(
                "fitness_advice", 
                message, 
                self.context
            )
            
            # 更新上下文
            self.update_context({
                "previous_intent": "fitness_advice",
                "previous_response": handler_result.get("content", ""),
                "fitness_topics": self._extract_fitness_topics(message, handler_result)
            })
            
            # 添加后续建议或相关问题
            response_with_suggestions = self._add_suggestions(handler_result.get("content", ""), message)
            handler_result["content"] = response_with_suggestions
            
            # 检查是否需要转换到其他状态
            if self._should_change_topic(handler_result):
                self.need_transition = True
                self.next_state_name = self._get_next_state(handler_result)
                
                return {
                    "response": handler_result.get("content", ""),
                    "intent_type": "fitness_advice",
                    "confidence": 1.0,
                    "transitioning": True,
                    "next_state": self.next_state_name,
                    "suggested_next_intents": handler_result.get("suggested_next_intents", [])
                }
            else:
                # 继续在当前状态
                return {
                    "response": handler_result.get("content", ""),
                    "intent_type": "fitness_advice",
                    "confidence": 1.0,
                    "transitioning": False,
                    "suggested_next_intents": handler_result.get("suggested_next_intents", [])
                }
        
        except Exception as e:
            logger.error(f"处理健身建议时出错: {str(e)}")
            self.need_transition = True
            self.next_state_name = "idle"
            
            return {
                "response": f"抱歉，处理您的健身建议请求时遇到了问题。请重新描述您的需求。",
                "intent_type": "error",
                "confidence": 0.0,
                "transitioning": True,
                "next_state": "idle"
            }
    
    def should_transition(self) -> bool:
        """
        检查是否应该转换到新状态
        
        Returns:
            如果需要转换，返回True；否则返回False
        """
        return self.need_transition
    
    def get_next_state(self) -> Type[ConversationState]:
        """
        获取下一个状态
        
        Returns:
            下一个状态的类
        """
        if not self.next_state_name or self.next_state_name == "idle":
            return IdleState
        
        # 根据状态名称导入相应的状态类
        if self.next_state_name == "training_plan":
            from app.services.ai_assistant.conversation.states.training_plan import TrainingPlanState
            return TrainingPlanState
        elif self.next_state_name == "exercise_action":
            from app.services.ai_assistant.conversation.states.exercise_action import ExerciseActionState
        elif self.next_state_name == "diet_advice":
            from app.services.ai_assistant.conversation.states.diet_advice import DietAdviceState
        else:
            return IdleState
    
    @classmethod
    def can_handle(cls, intent: str) -> bool:
        """
        检查该状态是否可以处理指定的意图
        
        Args:
            intent: 意图类型
            
        Returns:
            如果可以处理该意图，返回True；否则返回False
        """
        # 可以处理健身建议相关的意图
        fitness_advice_intents = [
            "fitness_advice", "fitness_goal", "workout_frequency",
            "fitness_level", "fitness_tips"
        ]
        return intent in fitness_advice_intents
    
    def _is_exit_intent(self, message: str) -> bool:
        """
        检查是否是退出当前状态的意图
        
        Args:
            message: 用户消息
            
        Returns:
            如果是退出意图，返回True；否则返回False
        """
        exit_phrases = [
            "谢谢", "退出", "结束", "返回", "回到主菜单", "不需要了",
            "OK", "好的", "够了", "明白了", "我知道了", "再见"
        ]
        
        return any(phrase in message for phrase in exit_phrases)
    
    def _should_change_topic(self, handler_result: Dict[str, Any]) -> bool:
        """
        检查是否应该更换话题
        
        Args:
            handler_result: 处理器返回的结果
            
        Returns:
            如果应该更换话题，返回True；否则返回False
        """
        # 检查处理器是否建议更换话题
        if "suggested_next_intents" in handler_result:
            suggested_intents = handler_result.get("suggested_next_intents", [])
            
            # 如果建议的下一个意图不是健身建议相关，可能需要更换话题
            for intent in suggested_intents:
                if not FitnessAdviceState.can_handle(intent):
                    return True
        
        return False
    
    def _get_next_state(self, handler_result: Dict[str, Any]) -> str:
        """
        根据处理结果确定下一个状态
        
        Args:
            handler_result: 处理器返回的结果
            
        Returns:
            下一个状态的名称
        """
        # 检查建议的下一个意图
        if "suggested_next_intents" in handler_result:
            suggested_intents = handler_result.get("suggested_next_intents", [])
            
            # 意图到状态的映射
            intent_to_state = {
                "training_plan": "training_plan",
                "exercise_action": "exercise_action",
                "diet_advice": "diet_advice"
            }
            
            # 检查是否有建议转换到的状态
            for intent in suggested_intents:
                if intent in intent_to_state:
                    return intent_to_state[intent]
        
        # 默认返回空闲状态
        return "idle"
    
    def _extract_fitness_topics(self, message: str, handler_result: Dict[str, Any]) -> List[str]:
        """
        从用户消息和处理结果中提取健身主题
        
        Args:
            message: 用户消息
            handler_result: 处理器返回的结果
            
        Returns:
            提取到的健身主题列表
        """
        # 从上下文中获取已有的主题
        existing_topics = self.context.get("fitness_topics", [])
        
        # 从处理结果中提取新主题
        new_topics = []
        
        # 关键词映射
        keyword_to_topic = {
            "力量": "力量训练",
            "肌肉": "增肌",
            "增肌": "增肌",
            "减脂": "减脂",
            "有氧": "有氧训练",
            "跑步": "跑步",
            "柔韧": "柔韧性",
            "拉伸": "拉伸",
            "瑜伽": "瑜伽",
            "健美": "健美",
            "塑形": "塑形",
            "训练计划": "训练计划"
        }
        
        # 检查消息中的关键词
        for keyword, topic in keyword_to_topic.items():
            if keyword in message and topic not in existing_topics and topic not in new_topics:
                new_topics.append(topic)
        
        # 合并新旧主题，最多保留5个
        all_topics = existing_topics + new_topics
        return all_topics[:5]
    
    def _needs_additional_info(self, message: str) -> bool:
        """
        检查是否需要额外信息才能提供更准确的健身建议
        
        Args:
            message: 用户消息
            
        Returns:
            如果需要额外信息，返回True；否则返回False
        """
        # 检查是否是首次对话
        if self.turn_count == 1:
            # 对于非常简短或一般性的问题，可能需要更多信息
            if len(message) < 15 or any(phrase in message for phrase in [
                "健身建议", "健身", "锻炼", "训练", "健康", "减肥", "增肌",
                "什么运动好", "应该怎么锻炼", "如何开始"
            ]):
                return True
        
        # 检查上下文中是否缺少关键信息
        missing_info = self._identify_missing_info(message)
        return len(missing_info) > 0 and self.turn_count < 3  # 只在前几轮尝试获取信息
    
    def _identify_missing_info(self, message: str) -> List[str]:
        """
        识别缺少的关键信息
        
        Args:
            message: 用户消息
            
        Returns:
            缺少的信息类型列表
        """
        missing_info = []
        user_info = self.context.get("user_info", {})
        
        # 检查重要的健身信息是否缺失
        if not user_info.get("fitness_goal"):
            missing_info.append("fitness_goal")
        
        if not user_info.get("fitness_level"):
            missing_info.append("fitness_level")
        
        if not user_info.get("workout_frequency"):
            missing_info.append("workout_frequency")
        
        # 只在用户询问具体健身计划时检查这些信息
        if any(phrase in message for phrase in ["计划", "方案", "安排", "训练"]):
            if not user_info.get("age"):
                missing_info.append("age")
            
            if not user_info.get("gender"):
                missing_info.append("gender")
            
            if not user_info.get("health_condition"):
                missing_info.append("health_condition")
        
        # 只在用户询问饮食建议时检查这些信息
        if any(phrase in message for phrase in ["饮食", "吃什么", "营养", "蛋白质", "碳水"]):
            if not user_info.get("diet_preference"):
                missing_info.append("diet_preference")
            
            if not user_info.get("weight"):
                missing_info.append("weight")
        
        return missing_info
    
    def _generate_proactive_question(self, message: str) -> str:
        """
        生成主动引导问题，获取更多信息
        
        Args:
            message: 用户消息
            
        Returns:
            主动引导问题
        """
        missing_info = self._identify_missing_info(message)
        
        # 如果是首次对话，给出更一般的引导
        if self.turn_count == 1 and len(message) < 15:
            return (
                "为了给您提供更准确的健身建议，请告诉我一些关于您的情况。"
                "您的健身目标是什么？您的健身水平如何（初级、中级或高级）？"
                "您每周能投入多少时间进行锻炼？"
            )
        
        # 根据缺失信息生成具体问题
        if "fitness_goal" in missing_info:
            return "为了给您提供更个性化的建议，请问您的健身目标是什么？是增肌、减脂、提高耐力还是其他目标？"
        
        if "fitness_level" in missing_info:
            return "请问您目前的健身水平是什么？是初学者，有一定基础，还是已经有丰富的健身经验？"
        
        if "workout_frequency" in missing_info:
            return "您每周大约能花多少时间进行锻炼？能锻炼几次？每次大约多长时间？"
        
        if "age" in missing_info:
            return "方便告诉我您的年龄范围吗？这有助于我为您提供更适合的健身计划。"
        
        if "health_condition" in missing_info:
            return "您是否有任何健康状况或伤病需要特别注意的？这对于制定安全的锻炼计划很重要。"
        
        if "diet_preference" in missing_info:
            return "关于饮食，您有什么特别的偏好或限制吗？比如素食、无麸质或其他饮食习惯？"
        
        # 默认问题
        return "能否告诉我更多关于您的健身情况和需求，这样我可以提供更有针对性的建议？"
    
    def _add_suggestions(self, response: str, message: str) -> str:
        """
        在回复中添加后续建议或相关问题
        
        Args:
            response: 原始回复
            message: 用户消息
            
        Returns:
            添加建议后的回复
        """
        # 避免在每条消息都添加建议
        if self.turn_count % 2 != 0:
            return response
        
        # 提取当前对话的主题
        fitness_topics = self.context.get("fitness_topics", [])
        
        # 根据主题提供相关建议
        suggestions = []
        
        if any(topic in ["workout", "exercise", "training"] for topic in fitness_topics):
            suggestions.append("您可能还想了解这些运动的正确姿势和技巧，这对避免受伤很重要。")
        
        if any(topic in ["diet", "nutrition", "protein"] for topic in fitness_topics):
            suggestions.append("除了饮食建议，合理的训练计划也是达成健身目标的关键。需要我为您制定训练计划吗？")
        
        if any(topic in ["weight_loss", "fat_burn"] for topic in fitness_topics):
            suggestions.append("减脂需要结合有氧运动和力量训练，您想了解更具体的减脂训练方案吗？")
        
        if any(topic in ["muscle_gain", "strength"] for topic in fitness_topics):
            suggestions.append("增肌还需要注意合理的营养摄入，特别是蛋白质。需要我给您提供相关的营养建议吗？")
        
        # 如果有建议，添加到回复中
        if suggestions:
            suggestion = suggestions[0]  # 只添加一个建议，避免过多
            return f"{response}\n\n{suggestion}"
        
        return response 
    
    async def handle_message(self, message: str, intent: Optional[str] = None, user_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理用户消息的接口适配器
        
        此方法用于兼容 orchestrator 的调用方式，调用具体状态的 process_message 方法
        
        Args:
            message: 用户输入的消息
            intent: 识别出的意图
            user_info: 用户信息
            
        Returns:
            处理结果，包含响应和新的状态信息
        """
        # 更新上下文中的用户信息
        if user_info:
            self.update_context({"user_info": user_info})
        
        # 如果提供了意图，记录到上下文
        if intent:
            self.update_context({"current_intent": intent})
        
        # 调用具体状态的处理方法
        result = await self.process_message(message)
        
        # 确保响应格式正确
        if isinstance(result, dict):
            if "response" in result and "response_content" not in result:
                result["response_content"] = result["response"]
            return result
        elif isinstance(result, str):
            return {
                "response_content": result,
                "response_type": "text",
                "intent_type": intent or "fitness_advice",
                "confidence": 1.0,
                "transitioning": self.need_transition,
                "next_state": self.next_state_name if self.need_transition else None
            }
        else:
            return {
                "response_content": "抱歉，我无法处理您的请求。",
                "response_type": "error",
                "intent_type": "error",
                "confidence": 0.0,
                "transitioning": False
            }