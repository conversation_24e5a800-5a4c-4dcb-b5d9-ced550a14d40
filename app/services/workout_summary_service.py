from sqlalchemy.orm import Session
from typing import Optional, List
from fastapi import Depends

from app import models, schemas, crud
from app.models.workout import WorkoutStatus
from datetime import datetime

class WorkoutSummaryService:
    """
    训练摘要服务，处理从Workout到DailyWorkout的转换，
    用于社区分享功能。
    """
    
    async def create_daily_workout_from_workout(
        self, 
        db: Session, 
        workout_id: int, 
        user_id: int, 
        title: Optional[str] = None, 
        content: Optional[str] = None, 
        visibility: str = "Everyone"
    ) -> Optional[models.DailyWorkout]:
        """
        从已完成的Workout创建DailyWorkout记录用于社区分享
        
        Args:
            db: 数据库会话
            workout_id: Workout ID
            user_id: 用户ID
            title: 自定义标题
            content: 自定义内容
            visibility: 可见性设置
            
        Returns:
            创建的DailyWorkout实例，如果Workout不存在或未完成则返回None
        """
        # 获取Workout
        workout = db.query(models.Workout).filter(models.Workout.id == workout_id).first()
        
        # 检查Workout是否存在且属于该用户
        if not workout:
            return None
            
        # 检查训练计划的用户身份（通过training_plan的user_id）
        # 这里假设有training_plan_id和training_plan关系，需要根据实际模型调整
        if hasattr(workout, 'training_plan') and workout.training_plan:
            if workout.training_plan.user_id != user_id:
                return None
        
        # 检查训练是否已完成
        if workout.status != WorkoutStatus.COMPLETED:
            return None
            
        # 如果已经有DailyWorkout，则返回现有的
        if workout.daily_workout:
            return workout.daily_workout
            
        # 创建新的DailyWorkout
        daily_workout = workout.create_daily_summary(
            user_id=user_id,
            title=title,
            content=content,
            visibility=visibility
        )
        
        db.add(daily_workout)
        db.commit()
        db.refresh(daily_workout)
        
        return daily_workout
        
    async def get_workout_summary(
        self,
        db: Session,
        daily_workout_id: int,
        current_user_id: Optional[int] = None
    ) -> Optional[models.DailyWorkout]:
        """
        获取训练摘要详情，考虑可见性设置
        
        Args:
            db: 数据库会话
            daily_workout_id: DailyWorkout ID
            current_user_id: 当前用户ID，用于检查可见性
            
        Returns:
            DailyWorkout实例，如果不存在或无权限查看则返回None
        """
        daily_workout = db.query(models.DailyWorkout).filter(
            models.DailyWorkout.id == daily_workout_id
        ).first()
        
        if not daily_workout:
            return None
            
        # 检查可见性
        if daily_workout.visibility != "Everyone" and daily_workout.user_id != current_user_id:
            return None
            
        return daily_workout 