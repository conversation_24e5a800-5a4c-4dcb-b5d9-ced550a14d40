from sqlalchemy import Column, Integer, String, <PERSON><PERSON><PERSON>, SmallInteger, Text
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.models.set_record import SetRecord  # 确保正确导入

class WorkoutExercise(Base):
    """训练动作模型，代表训练日中的具体动作安排"""
    __tablename__ = "workout_exercises"

    id = Column(Integer, primary_key=True, index=True)
    workout_id = Column(Integer, ForeignKey("workouts.id"), nullable=True)
    daily_workout_id = Column(Integer, ForeignKey("daily_workouts.id"), nullable=True)
    exercise_id = Column(Integer, ForeignKey("exercises.id"))
    sets = Column(SmallInteger, nullable=False, default=3)
    reps = Column(String(50), nullable=False, default="10")
    rest_seconds = Column(SmallInteger, nullable=True, default=60)
    weight = Column(String(50), nullable=True)
    order = Column(SmallInteger, nullable=False, default=1)
    notes = Column(Text, nullable=True)
    exercise_type = Column(String(50), nullable=False, default="weight_reps")
    superset_group = Column(Integer, nullable=True)

    # 确保关系定义正确
    workout = relationship("Workout", back_populates="workout_exercises")
    daily_workout = relationship("DailyWorkout", back_populates="workout_exercises")
    exercise = relationship("Exercise", back_populates="workout_exercises")
    set_records = relationship("SetRecord", back_populates="workout_exercise")

    def __repr__(self):
        return f"<WorkoutExercise {self.id}: {self.exercise_id} in Workout {self.workout_id}>"

    # 注意: 与跟踪已执行组相关的数据（例如：实际重量、实际次数、完成状态）
    # 应属于单独的 'SetLog' 表，不属于此计划定义模型的一部分。
