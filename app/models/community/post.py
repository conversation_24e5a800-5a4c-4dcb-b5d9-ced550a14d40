from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Enum, Integer
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import enum
from datetime import datetime
from typing import TYPE_CHECKING

# 避免循环导入
if TYPE_CHECKING:
    from app.models.community.report import Report, PostReport

class PostStatus(enum.IntEnum):
    ACTIVE = 1
    DELETED = 2
    REPORTED = 3

class Post(Base):
    __tablename__ = "posts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(100), nullable=False)
    content = Column(String(5000), nullable=False)
    related_workout_id = Column(Integer, ForeignKey("daily_workouts.id"), nullable=True)
    like_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    visibility = Column(String(20), default="Everyone", nullable=False)
    status = Column(Enum(PostStatus), default=PostStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="posts")
    related_workout = relationship("DailyWorkout", back_populates="posts")
    images = relationship("Image", back_populates="post")
    comments = relationship("Comment", back_populates="post")
    likes = relationship("PostLike", back_populates="post")
    reports = relationship("Report", foreign_keys="Report.post_id", back_populates="post")