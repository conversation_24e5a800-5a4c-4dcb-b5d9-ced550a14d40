from app.db.base_class import Base

# 基础模型
from app.models.user_setting import UserSetting
from app.models.user_stats import UserStats
from app.models.feedback import Feedback

# 健身和健康相关模型
from app.models.exercise import Exercise
from app.models.food import Food
from app.models.favorite import UserFavoriteExercise, UserFavoriteFood
from app.models.training_plan import TrainingPlan
from app.models.workout import Workout
from app.models.set_record import SetRecord  # 确保在 WorkoutExercise 之前导入
from app.models.workout_exercise import WorkoutExercise
from app.models.meal import MealRecord, FoodItem, FoodItemNutrientIntake, HealthRecommendation, FoodRecognition
from app.models.share_track import ShareTrack

# 用户相关模型
from app.models.user import User, Gender, ExperienceLevel, FitnessGoal

# LangChain相关模型
from app.models.conversation import Conversation
from app.models.message import Message, MessageRole
from app.models.qa_pair import QAPair
from app.models.user_training_plan_record import UserTrainingPlanRecord
from app.models.training_template import TrainingTemplate 

# 新增模型
from app.models.user_training_record import UserTrainingRecord

# 单日训练模型
from app.models.daily_workout import DailyWorkout, DailyWorkoutStatus

# 社区相关模型
from app.models.community.post import Post, PostStatus
from app.models.community.comment import Comment, CommentStatus
from app.models.community.notification import Notification, NotificationType
from app.models.community.user_relation import UserRelation
from app.models.community.image import Image

# 确保所有相关模型都被正确导入
__all__ = [
    "User",
    "Exercise",
    "Workout",
    "SetRecord",
    "WorkoutExercise",
    "DailyWorkout",
    "Post",
    "Comment",
    "Notification",
    "UserRelation",
    "Image"
]
