from sqlalchemy import Column, Integer, String, J<PERSON><PERSON>, DateTime, ForeignKey, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class TrainingTemplate(Base):
    """训练模板表"""
    __tablename__ = "training_templates"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    template_name = Column(String(255), nullable=False)
    exercises = Column(JSON)  # 存储训练动作信息的JSON数组
    notes = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    user = relationship("User", back_populates="training_templates")

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "template_name": self.template_name,
            "exercises": self.exercises,
            "notes": self.notes,
            "exercise_count": len(self.exercises) if self.exercises else 0,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 