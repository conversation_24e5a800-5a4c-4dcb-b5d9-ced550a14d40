from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from datetime import datetime, date

from app.db.session import get_db
from app.models.user import User
from app.models.user_training_plan_record import UserTrainingPlanRecord
from app.api.deps import get_current_user
from app.models.exercise import Exercise

router = APIRouter()

@router.get("/", response_model=List[dict])
def get_user_training_plan_records(
    date: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的训练计划记录
    
    参数:
    - date: 可选，指定日期（格式：YYYY-MM-DD）
    """
    try:
        # 构建查询
        query = db.query(UserTrainingPlanRecord).filter(
            UserTrainingPlanRecord.user_id == current_user.id
        )
        
        # 如果指定了日期，添加日期过滤
        if date:
            try:
                target_date = datetime.strptime(date, "%Y-%m-%d").date()
                query = query.filter(UserTrainingPlanRecord.date == target_date)
            except ValueError:
                raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")
        
        # 按日期降序和创建时间降序排序，确保最新的记录在前
        records = query.order_by(
            UserTrainingPlanRecord.date.desc(),
            UserTrainingPlanRecord.created_at.desc()
        ).all()
        
        # 使用字典进行去重，保留每个训练动作的最新记录
        unique_records = {}
        for record in records:
            key = f"{record.date}_{record.exercise_id}"
            if key not in unique_records:
                unique_records[key] = record
        
        # 转换为列表并按创建时间排序
        unique_records_list = sorted(
            unique_records.values(),
            key=lambda x: x.created_at,
            reverse=True
        )
        
        # 转换为字典格式
        return [record.to_dict() for record in unique_records_list]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取训练计划记录失败: {str(e)}")

@router.post("/", response_model=dict)
def create_training_plan_record(
    record_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新的训练计划记录
    """
    try:
        # 检查是否已存在相同日期和训练动作的记录
        target_date = datetime.strptime(record_data.get("date"), "%Y-%m-%d").date() if record_data.get("date") else date.today()
        existing_record = db.query(UserTrainingPlanRecord).filter(
            UserTrainingPlanRecord.user_id == current_user.id,
            UserTrainingPlanRecord.exercise_id == record_data.get("exercise_id"),
            UserTrainingPlanRecord.date == target_date
        ).first()
        
        if existing_record:
            # 如果存在，更新现有记录
            exercise = db.query(Exercise).filter(Exercise.id == record_data.get("exercise_id")).first()
            if not exercise:
                raise HTTPException(status_code=400, detail="指定的练习不存在")
                
            for key, value in record_data.items():
                if hasattr(existing_record, key):
                    setattr(existing_record, key, value)
            existing_record.name = exercise.name
            db.commit()
            db.refresh(existing_record)
            return existing_record.to_dict()
        else:
            # 如果不存在，创建新记录
            exercise = db.query(Exercise).filter(Exercise.id == record_data.get("exercise_id")).first()
            if not exercise:
                raise HTTPException(status_code=400, detail="指定的练习不存在")
                
            new_record = UserTrainingPlanRecord(
                user_id=current_user.id,
                exercise_id=record_data.get("exercise_id"),
                name=exercise.name,
                date=target_date,
                sets=record_data.get("sets", []),
                total_sets=record_data.get("total_sets", 0),
                muscle_group=record_data.get("muscle_group"),
                notes=record_data.get("notes")
            )
            
            db.add(new_record)
            db.commit()
            db.refresh(new_record)
            return new_record.to_dict()
            
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建训练计划记录失败: {str(e)}")

@router.delete("/{record_id}")
def delete_training_plan_record(
    record_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除训练计划记录
    """
    try:
        record = db.query(UserTrainingPlanRecord).filter(
            UserTrainingPlanRecord.id == record_id,
            UserTrainingPlanRecord.user_id == current_user.id
        ).first()
        
        if not record:
            raise HTTPException(status_code=404, detail="训练计划记录不存在")
            
        db.delete(record)
        db.commit()
        
        return {"message": "训练计划记录已删除"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除训练计划记录失败: {str(e)}")

@router.post("/batch-delete", response_model=dict)
def batch_delete_training_plan_records(
    record_ids: List[int],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    批量删除训练计划记录
    
    参数:
    - record_ids: 要删除的记录ID列表
    """
    try:
        if not record_ids:
            raise HTTPException(status_code=400, detail="记录ID列表不能为空")
            
        # 找出所有属于当前用户的记录
        records = db.query(UserTrainingPlanRecord).filter(
            UserTrainingPlanRecord.id.in_(record_ids),
            UserTrainingPlanRecord.user_id == current_user.id
        ).all()
        
        # 检查是否找到所有记录
        found_ids = {record.id for record in records}
        not_found_ids = set(record_ids) - found_ids
        
        if not_found_ids:
            raise HTTPException(status_code=404, detail=f"未找到以下记录: {list(not_found_ids)}")
        
        # 删除找到的所有记录
        for record in records:
            db.delete(record)
            
        db.commit()
        
        return {"message": f"成功删除 {len(records)} 条训练计划记录"}
        
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量删除训练计划记录失败: {str(e)}") 