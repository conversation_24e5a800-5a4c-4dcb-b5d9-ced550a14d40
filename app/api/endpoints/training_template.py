from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from datetime import datetime

from app.db.session import get_db
from app.models.user import User
from app.models.training_template import TrainingTemplate
from app.models.user_training_plan_record import UserTrainingPlanRecord
from app.models.exercise import Exercise
from app.api.deps import get_current_user

router = APIRouter()

@router.get("/", response_model=List[dict])
def get_training_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的所有训练模板
    """
    try:
        templates = db.query(TrainingTemplate).filter(
            TrainingTemplate.user_id == current_user.id
        ).order_by(TrainingTemplate.created_at.desc()).all()
        
        return [template.to_dict() for template in templates]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取训练模板失败: {str(e)}")

@router.get("/{template_id}", response_model=dict)
def get_training_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取特定训练模板的详情
    """
    try:
        template = db.query(TrainingTemplate).filter(
            TrainingTemplate.id == template_id,
            TrainingTemplate.user_id == current_user.id
        ).first()
        
        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")
            
        return template.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取训练模板详情失败: {str(e)}")

@router.post("/", response_model=dict)
def create_training_template(
    template_data: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新的训练模板
    
    参数:
    - template_name: 模板名称
    - exercises: 训练动作信息数组
    - notes: 备注(可选)
    """
    try:
        template_name = template_data.get("template_name")
        exercises = template_data.get("exercises")
        notes = template_data.get("notes")
        
        if not template_name:
            raise HTTPException(status_code=400, detail="模板名称不能为空")
            
        if not exercises or not isinstance(exercises, list) or len(exercises) == 0:
            raise HTTPException(status_code=400, detail="训练动作不能为空")
        
        # 创建新的训练模板
        new_template = TrainingTemplate(
            user_id=current_user.id,
            template_name=template_name,
            exercises=exercises,
            notes=notes
        )
        
        db.add(new_template)
        db.commit()
        db.refresh(new_template)
        
        return new_template.to_dict()
        
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建训练模板失败: {str(e)}")

@router.delete("/{template_id}")
def delete_training_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除训练模板
    """
    try:
        template = db.query(TrainingTemplate).filter(
            TrainingTemplate.id == template_id,
            TrainingTemplate.user_id == current_user.id
        ).first()
        
        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权删除")
            
        db.delete(template)
        db.commit()
        
        return {"message": "训练模板已删除"}
        
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除训练模板失败: {str(e)}")

@router.post("/{template_id}/apply", response_model=List[dict])
def apply_training_template(
    template_id: int,
    date: Optional[str] = Query(None),
    body: Optional[dict] = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    应用训练模板到指定日期
    
    参数:
    - template_id: 模板ID
    - date: 应用日期(YYYY-MM-DD)，可以作为查询参数或请求体参数提供
    """
    try:
        # 从查询参数或请求体中获取日期
        date_value = date
        if not date_value and body and "date" in body:
            date_value = body.get("date")
            
        if not date_value:
            raise HTTPException(status_code=400, detail="缺少必要参数: date")
            
        # 验证日期格式
        try:
            target_date = datetime.strptime(date_value, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")
        
        # 获取模板
        template = db.query(TrainingTemplate).filter(
            TrainingTemplate.id == template_id,
            TrainingTemplate.user_id == current_user.id
        ).first()
        
        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")
            
        # 获取模板中的训练动作
        if not template.exercises or len(template.exercises) == 0:
            raise HTTPException(status_code=400, detail="该训练模板中没有训练动作")
        
        # 创建新记录
        created_records = []
        for exercise_data in template.exercises:
            # 获取训练动作信息
            exercise_id = exercise_data.get("exerciseId") or exercise_data.get("exercise_id")
            if not exercise_id:
                continue
                
            # 查询训练动作详情
            exercise = db.query(Exercise).filter(Exercise.id == exercise_id).first()
            if not exercise:
                continue
                
            # 检查是否已存在相同日期和训练动作的记录
            existing_record = db.query(UserTrainingPlanRecord).filter(
                UserTrainingPlanRecord.user_id == current_user.id,
                UserTrainingPlanRecord.exercise_id == exercise_id,
                UserTrainingPlanRecord.date == target_date
            ).first()
            
            if existing_record:
                # 更新现有记录
                existing_record.sets = exercise_data.get("sets") or []
                existing_record.notes = exercise_data.get("notes") or ""
                db.commit()
                db.refresh(existing_record)
                created_records.append(existing_record)
            else:
                # 创建新记录
                new_record = UserTrainingPlanRecord(
                    user_id=current_user.id,
                    exercise_id=exercise_id,
                    name=exercise.name,
                    date=target_date,
                    sets=exercise_data.get("sets") or [],
                    total_sets=len(exercise_data.get("sets") or []),
                    notes=exercise_data.get("notes") or ""
                )
                db.add(new_record)
                db.commit()
                db.refresh(new_record)
                created_records.append(new_record)
        
        return [record.to_dict() for record in created_records]
        
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"应用训练模板失败: {str(e)}") 