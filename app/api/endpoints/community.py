from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form, Query, Path
from sqlalchemy.orm import Session
from fastapi.responses import FileResponse
import os

from app import crud, models, schemas
from app.api import deps
from app.services.community_service import CommunityService
from app.services.workout_summary_service import WorkoutSummaryService
from app.core.config import settings
from app.schemas.community import (
    DailyWorkoutCreate, DailyWorkoutUpdate, DailyWorkoutResponse,
    PostCreate, PostUpdate, PostResponse,
    CommentCreate, CommentResponse,
    NotificationResponse,
    UserRelationResponse,
    ImageCreate, ImageUpdate, ImageResponse,
    WorkoutShareCreate
)
from app.models.community.notification import NotificationType

router = APIRouter()

# Workout Share Endpoints
@router.post("/workouts/{workout_id}/share", response_model=DailyWorkoutResponse)
async def share_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_id: int,
    share_data: WorkoutShareCreate,
    workout_summary_service: WorkoutSummaryService = Depends()
):
    """从已完成的训练创建并分享到社区"""
    daily_workout = await workout_summary_service.create_daily_workout_from_workout(
        db=db,
        workout_id=workout_id,
        user_id=current_user.id,
        title=share_data.title,
        content=share_data.content,
        visibility=share_data.visibility
    )
    
    if not daily_workout:
        raise HTTPException(status_code=404, detail="Workout not found or not completed")
        
    return daily_workout

# Daily Workout Endpoints
@router.post("/daily-workouts/", response_model=DailyWorkoutResponse)
async def create_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_in: DailyWorkoutCreate
):
    service = CommunityService(db)
    return await service.create_daily_workout(current_user.id, workout_in)

@router.put("/daily-workouts/{workout_id}", response_model=DailyWorkoutResponse)
async def update_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_id: int,
    workout_in: DailyWorkoutUpdate
):
    service = CommunityService(db)
    return await service.update_daily_workout(workout_id, current_user.id, workout_in)

@router.delete("/daily-workouts/{workout_id}")
async def delete_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_id: int
):
    service = CommunityService(db)
    return await service.delete_daily_workout(workout_id, current_user.id)

@router.get("/daily-workouts/{workout_id}", response_model=DailyWorkoutResponse)
async def get_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int
):
    service = CommunityService(db)
    return await service.get_daily_workout(workout_id)

@router.get("/daily-workouts/", response_model=List[DailyWorkoutResponse])
async def get_daily_workouts(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100
):
    service = CommunityService(db)
    return await service.get_daily_workouts(skip, limit)

@router.get("/daily-workouts/search/", response_model=List[DailyWorkoutResponse])
async def search_daily_workouts(
    *,
    db: Session = Depends(deps.get_db),
    keyword: str,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.search_daily_workouts(keyword, skip, limit)

# Post Endpoints
@router.post("/posts/", response_model=PostResponse)
async def create_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_in: PostCreate
):
    service = CommunityService(db)
    return await service.create_post_with_workout(current_user.id, post_in)

@router.put("/posts/{post_id}", response_model=PostResponse)
async def update_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    post_in: PostUpdate
):
    service = CommunityService(db)
    return await service.update_post(post_id, current_user.id, post_in)

@router.delete("/posts/{post_id}")
async def delete_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int
):
    service = CommunityService(db)
    return await service.delete_post(post_id, current_user.id)

@router.get("/posts/{post_id}", response_model=PostResponse)
async def get_post(
    *,
    db: Session = Depends(deps.get_db),
    post_id: int
):
    service = CommunityService(db)
    return await service.get_post(post_id)

@router.get("/posts/", response_model=List[PostResponse])
async def get_posts(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.get_posts(skip, limit)

@router.get("/posts/search/", response_model=List[PostResponse])
async def search_posts(
    *,
    db: Session = Depends(deps.get_db),
    keyword: str,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.search_posts(keyword, skip, limit)

@router.post("/posts/{post_id}/like/")
async def like_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int
):
    service = CommunityService(db)
    return await service.like_post(post_id, current_user.id)

@router.post("/posts/{post_id}/report/")
async def report_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    reason: str
):
    service = CommunityService(db)
    return await service.report_post(post_id, current_user.id, reason)

# Comment Endpoints
@router.post("/posts/{post_id}/comments/", response_model=CommentResponse)
async def create_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    comment_in: CommentCreate
):
    service = CommunityService(db)
    return await service.create_comment(current_user.id, comment_in)

@router.put("/comments/{comment_id}", response_model=CommentResponse)
async def update_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int,
    content: str
):
    service = CommunityService(db)
    return await service.update_comment(comment_id, current_user.id, content)

@router.delete("/comments/{comment_id}")
async def delete_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int
):
    service = CommunityService(db)
    return await service.delete_comment(comment_id, current_user.id)

@router.post("/comments/{comment_id}/like/")
async def like_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int
):
    service = CommunityService(db)
    return await service.like_comment(comment_id, current_user.id)

@router.post("/comments/{comment_id}/report/")
async def report_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int,
    reason: str
):
    service = CommunityService(db)
    return await service.report_comment(comment_id, current_user.id, reason)

# Notification Endpoints
@router.get("/notifications/", response_model=List[NotificationResponse])
async def get_notifications(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.get_notifications(current_user.id, skip, limit)

@router.get("/notifications/filter/", response_model=List[NotificationResponse])
async def filter_notifications(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    type: NotificationType,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.filter_notifications(current_user.id, type, skip, limit)

@router.patch("/notifications/{notification_id}/read/")
async def mark_notification_as_read(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    notification_id: int
):
    service = CommunityService(db)
    return await service.mark_notification_as_read(notification_id, current_user.id)

@router.patch("/notifications/read-all/")
async def mark_all_notifications_as_read(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user)
):
    service = CommunityService(db)
    return await service.mark_all_notifications_as_read(current_user.id)

@router.delete("/notifications/{notification_id}")
async def delete_notification(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    notification_id: int
):
    service = CommunityService(db)
    return await service.delete_notification(notification_id, current_user.id)

# User Relation Endpoints
@router.post("/users/{user_id}/follow/")
async def follow_user(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    user_id: int
):
    service = CommunityService(db)
    return await service.follow_user(current_user.id, user_id)

@router.delete("/users/{user_id}/follow/")
async def unfollow_user(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    user_id: int
):
    service = CommunityService(db)
    return await service.unfollow_user(current_user.id, user_id)

@router.get("/users/{user_id}/following/", response_model=List[UserRelationResponse])
async def get_following(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.get_following(user_id, skip, limit)

@router.get("/users/{user_id}/followers/", response_model=List[UserRelationResponse])
async def get_followers(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.get_followers(user_id, skip, limit)

# Image Endpoints
@router.post("/images/", response_model=ImageResponse)
async def create_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    image_in: ImageCreate
):
    service = CommunityService(db)
    return await service.create_image(current_user.id, image_in)

@router.put("/images/{image_id}", response_model=ImageResponse)
async def update_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    image_id: int,
    image_in: ImageUpdate
):
    service = CommunityService(db)
    return await service.update_image(image_id, current_user.id, image_in)

@router.delete("/images/{image_id}")
async def delete_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    image_id: int
):
    service = CommunityService(db)
    return await service.delete_image(image_id, current_user.id)
