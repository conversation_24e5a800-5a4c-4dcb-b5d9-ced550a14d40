"""
聊天API端点模块 (V2)

提供与重构后AI助手系统对话的REST和WebSocket接口
"""

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, BackgroundTasks, WebSocket, WebSocketDisconnect, Query, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
import uuid
import logging
import asyncio
import json
import time

from app import schemas, models, crud
from app.api import deps
from app.models.message import MessageRole
from app.adapters.v2_adapter import adapt_conversation_response, adapt_message_format, adapt_conversation_history
from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
from app.utils.time_utils import get_utc_now

# 设置日志记录器
logger = logging.getLogger(__name__)

router = APIRouter()

# ============== 请求/响应模型 ==============

class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息内容")
    session_id: Optional[str] = Field(None, description="会话ID，如果为空则创建新会话")
    meta_info: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    quick_intent: Optional[str] = Field(None, description="快速意图参数")
    system_prompt: Optional[str] = Field(None, description="系统提示词")
    
class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str = Field(..., description="AI助手响应内容")
    conversation_id: str = Field(..., description="会话ID")
    session_id: str = Field(..., description="会话ID（兼容性）")
    intent_type: Optional[str] = Field(None, description="识别的意图类型")
    confidence: Optional[float] = Field(None, description="意图识别置信度")
    success: bool = Field(True, description="处理是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    meta_info: Optional[Dict[str, Any]] = Field(None, description="响应元数据")
    end_conversation: bool = Field(False, description="是否结束对话")
    next_intent: Optional[str] = Field(None, description="下一个需要处理的意图")
    missing_parameters: Optional[list] = Field(None, description="缺失的参数列表")

class UserInfoUpdateRequest(BaseModel):
    """用户信息更新请求模型"""
    session_id: str = Field(..., description="会话ID")
    field: str = Field(..., description="要更新的字段名")
    value: Any = Field(..., description="字段值")
    value_text: str = Field(..., description="用户输入的原始文本，用于保存消息记录")

class TrainingPlanRequest(BaseModel):
    """训练计划生成请求模型"""
    session_id: str = Field(..., description="会话ID")
    plan_type: str = Field("single_day", description="计划类型：single_day, weekly")
    body_part: Optional[str] = Field(None, description="目标训练部位")
    training_scene: Optional[str] = Field("gym", description="训练场景：gym, home")
    duration_weeks: Optional[int] = Field(4, description="计划持续周数")
    days_per_week: Optional[int] = Field(3, description="每周训练天数")
    available_time: Optional[int] = Field(60, description="可用时间（分钟）")
    additional_notes: Optional[str] = Field(None, description="额外备注")

# ============== 辅助函数 ==============

def get_or_create_conversation(
    db: Session, 
    session_id: str, 
    user_id: int,
    system_prompt: Optional[str] = None
) -> tuple[models.Conversation, bool]:
    """获取或创建会话"""
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    is_new = False
    
    if not conversation:
        # 创建新会话
        conversation = crud.crud_conversation.create_with_session_id(
            db,
            obj_in=schemas.ConversationCreate(
                session_id=session_id,
                user_id=user_id,
                system_prompt=system_prompt
            ),
            session_id=session_id,
            user_id=user_id
        )
        is_new = True
    elif conversation.user_id != user_id:
        # 会话不属于当前用户
        raise HTTPException(status_code=403, detail="无权访问此会话")
    
    return conversation, is_new

async def create_user_message(
    db: Session,
    conversation_id: int,
    user_id: int,
    content: str,
    meta_info: Optional[Dict[str, Any]] = None
) -> models.Message:
    """创建用户消息"""
    return crud.crud_message.create_with_conversation(
        db,
        obj_in=schemas.MessageCreate(
            content=content,
            role=MessageRole.USER,
            meta_info=meta_info or {},
            user_id=user_id,
            conversation_id=conversation_id
        ),
        conversation_id=conversation_id,
        user_id=user_id
    )

async def create_assistant_message(
    db: Session,
    conversation_id: int,
    user_id: int,
    content: str,
    meta_info: Optional[Dict[str, Any]] = None
) -> models.Message:
    """创建AI助手消息"""
    return crud.crud_message.create_with_conversation(
        db,
        obj_in=schemas.MessageCreate(
            content=content,
            role=MessageRole.ASSISTANT,
            meta_info=meta_info or {},
            user_id=user_id,
            conversation_id=conversation_id
        ),
        conversation_id=conversation_id,
        user_id=user_id
    )

# ============== API端点实现 ==============

@router.post("/message", response_model=ChatResponse)
async def send_message(
    *,
    chat_request: ChatRequest = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    向AI助手发送消息并获取回复
    
    通过新版AI助手系统处理用户消息，支持快速意图参数
    """
    try:
        session_id = chat_request.session_id or str(uuid.uuid4())
        
        # 获取或创建会话
        conversation, is_new = get_or_create_conversation(
            db, 
            session_id, 
            current_user.id,
            system_prompt=chat_request.system_prompt
        )
        
        # 保存用户消息到数据库
        user_message = await create_user_message(
            db,
            conversation.id,
            current_user.id,
            chat_request.message,
            chat_request.meta_info
        )
        
        # 准备用户信息
        user_info = {
            "user_id": str(current_user.id),
            "nickname": current_user.nickname,
            "age": current_user.age,
            "gender": current_user.gender,
            "height": current_user.height,
            "weight": current_user.weight,
            "fitness_goal": current_user.fitness_goal,
            "experience_level": current_user.experience_level,
            "activity_level": current_user.activity_level
        }
        
        # 使用新版AI助手处理消息
        response = await conversation_orchestrator.process_message(
            message=chat_request.message,
            conversation_id=session_id,
            user_info=user_info
        )
        
        # 提取响应内容
        response_content = response.get("response_content", response.get("response", ""))
        
        # 保存AI回复到数据库
        if response_content:
            await create_assistant_message(
                db,
                conversation.id,
                current_user.id,
                response_content,
                {
                    "intent": response.get("intent"),
                    "confidence": response.get("confidence"),
                    "current_state": response.get("current_state"),
                    "next_state": response.get("next_state")
                }
            )
        
        return ChatResponse(
            response=response_content,
            conversation_id=session_id,
            session_id=session_id,  # 兼容性
            intent_type=response.get("intent"),
            confidence=response.get("confidence"),
            success=True,
            meta_info=response
        )
        
    except Exception as e:
        logger.error(f"处理消息时出错: {str(e)}", exc_info=True)
        return ChatResponse(
            response="抱歉，处理您的消息时出现了错误。",
            conversation_id=chat_request.session_id or str(uuid.uuid4()),
            session_id=chat_request.session_id or str(uuid.uuid4()),
            success=False,
            error=str(e)
        )

@router.post("/update_user_info", response_model=ChatResponse)
async def update_user_info(
    *,
    db: Session = Depends(deps.get_db),
    update_request: UserInfoUpdateRequest = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新用户信息并继续对话
    
    用于处理信息收集过程中用户提供的信息
    """
    try:
        # 验证会话
        conversation = crud.crud_conversation.get_by_session_id(db, session_id=update_request.session_id)
        if not conversation or conversation.user_id != current_user.id:
            raise HTTPException(status_code=404, detail="会话未找到")

        # 更新会话的最后活跃时间
        crud.crud_conversation.update_last_active(db, conversation_id=conversation.id)

        # 保存用户消息
        user_message = await create_user_message(
            db,
            conversation.id,
            current_user.id,
            update_request.value_text,
            {"field_update": update_request.field}
        )

        # 更新用户信息
        from app.crud.crud_user import update_user_info as update_user_field
        updated_user = update_user_field(
            db, 
            user_id=current_user.id, 
            field=update_request.field, 
            value=update_request.value
        )
        
        if not updated_user:
            raise HTTPException(status_code=400, detail="更新用户信息失败")

        # 生成响应
        field_name_map = {
            "height": "身高",
            "weight": "体重", 
            "age": "年龄",
            "gender": "性别",
            "fitness_goal": "健身目标",
            "experience_level": "健身经验",
            "activity_level": "活动水平"
        }
        
        field_display = field_name_map.get(update_request.field, update_request.field)
        response_text = f"您的{field_display}已成功更新。我们继续为您提供个性化的健身建议。"

        # 保存AI回复
        await create_assistant_message(
            db,
            conversation.id,
            current_user.id,
            response_text,
            {"updated_field": update_request.field, "value": update_request.value}
        )

        return ChatResponse(
            response=response_text,
            conversation_id=update_request.session_id,
            session_id=update_request.session_id,
            success=True,
            meta_info={"updated_field": update_request.field, "value": update_request.value}
        )
        
    except Exception as e:
        logger.error(f"更新用户信息时出错: {str(e)}", exc_info=True)
        return ChatResponse(
            response="抱歉，更新信息时出现了错误。",
            conversation_id=update_request.session_id,
            session_id=update_request.session_id,
            success=False,
            error=str(e)
        )

@router.get("/conversations", response_model=schemas.ConversationList)
def get_conversations(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前用户的所有活跃会话
    """
    conversations = crud.crud_conversation.get_active_by_user(
        db, user_id=current_user.id, skip=skip, limit=limit
    )

    total = crud.crud_conversation.count_by_user(db, user_id=current_user.id)

    return {
        "conversations": conversations,
        "total": total
    }

@router.get("/sessions/{session_id}/messages", response_model=schemas.MessageList)
def get_conversation_messages(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    skip: int = 0,
    limit: int = 20,  # 默认获取20条
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取指定会话的消息历史
    
    如果会话不存在则自动创建
    """
    # 获取或创建会话
    conversation, is_new = crud.crud_conversation.get_or_create_by_session_id(
        db, session_id=session_id, user_id=current_user.id
    )
    
    # 计算总消息数
    total = crud.crud_message.count_by_conversation(db, conversation_id=conversation.id)
    
    # 获取消息，使用倒序方法
    messages = crud.crud_message.get_conversation_messages_desc(
        db, conversation_id=conversation.id, skip=skip, limit=limit
    )
    
    # 确保每个消息对象的元数据字段是字典而不是None
    formatted_messages = []
    for message in messages:
        # 使用适配器处理消息格式
        formatted_message = adapt_message_format(message.__dict__)
        formatted_messages.append(formatted_message)
    
    return {
        "messages": formatted_messages,
        "total": total
    }

@router.get("/conversations/{session_id}/messages/since/{message_id}", response_model=schemas.MessageList)
def get_new_messages_since(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    message_id: int,
    limit: int = 20,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取指定消息ID之后的新消息
    """
    # 验证会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话未找到")
    
    # 获取新消息
    messages = crud.crud_message.get_new_messages(
        db, conversation_id=conversation.id, last_message_id=message_id
    )
    
    # 处理消息格式
    formatted_messages = []
    for message in messages:
        formatted_message = adapt_message_format(message.__dict__)
        formatted_messages.append(formatted_message)
    
    return {
        "messages": formatted_messages,
        "total": len(formatted_messages)
    }

@router.delete("/conversations/{session_id}")
def delete_conversation(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除指定会话
    """
    # 验证会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="会话未找到")
    
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权删除此会话")
    
    # 删除会话（级联删除消息）
    crud.crud_conversation.remove(db, id=conversation.id)
    
    return {"message": "会话已删除", "session_id": session_id}

@router.get("/poll/{session_id}", response_model=schemas.MessageList)
def poll_new_messages(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    last_message_id: Optional[int] = Query(None, description="上次接收到的最后一条消息的ID"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    轮询获取会话中的新消息
    
    用于不支持WebSocket的环境，客户端可以定期调用此端点获取自上次轮询以来的新消息
    """
    # 转换last_message_id为整数
    try:
        message_id = int(last_message_id) if last_message_id else 0
    except (ValueError, TypeError):
        message_id = 0
    
    # 验证会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    
    # 如果会话不存在或不属于当前用户，返回404
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话未找到")
    
    # 获取新消息
    messages = crud.crud_message.get_new_messages(
        db, conversation_id=conversation.id, last_message_id=message_id
    )
    
    # 处理消息格式
    formatted_messages = []
    for message in messages:
        # 使用适配器处理消息格式
        formatted_message = adapt_message_format(message.__dict__)
        formatted_messages.append(formatted_message)
    
    return {
        "messages": formatted_messages,
        "total": len(formatted_messages)
    }

@router.get("/recent-messages", response_model=schemas.RecentMessageList)
def get_recent_messages(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 5,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户最近的消息
    """
    # 获取用户最近的消息
    messages = crud.crud_message.get_recent_by_user(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    
    # 格式化消息
    formatted_messages = []
    for message in messages:
        formatted_message = adapt_message_format(message.__dict__)
        formatted_messages.append(formatted_message)
    
    return {
        "messages": formatted_messages,
        "total": len(formatted_messages)
    }

@router.get("/recent-conversations", response_model=schemas.RecentMessageList)
def get_recent_conversations(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 5,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户最近的会话
    """
    # 获取用户最近的会话
    conversations = crud.crud_conversation.get_recent_by_user(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    
    # 为每个会话获取最后一条消息
    recent_messages = []
    for conversation in conversations:
        last_message = crud.crud_message.get_last_by_conversation(
            db, conversation_id=conversation.id
        )
        if last_message:
            formatted_message = adapt_message_format(last_message.__dict__)
            formatted_message["session_id"] = conversation.session_id
            recent_messages.append(formatted_message)
    
    return {
        "messages": recent_messages,
        "total": len(recent_messages)
    }

@router.post("/sessions/{session_id}/messages", response_model=schemas.Message)
def add_message(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    message_data: schemas.MessageBase = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    向指定会话添加消息
    """
    # 验证会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="会话未找到")
    
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此会话")
    
    # 创建消息
    message = crud.crud_message.create_with_conversation(
        db,
        obj_in=schemas.MessageCreate(
            content=message_data.content,
            role=message_data.role,
            meta_info=message_data.meta_info or {},
            user_id=current_user.id,
            conversation_id=conversation.id
        ),
        conversation_id=conversation.id,
        user_id=current_user.id
    )
    
    return message

@router.put("/messages/{message_id}", response_model=schemas.Message)
def update_message(
    *,
    db: Session = Depends(deps.get_db),
    message_id: int,
    message_update: schemas.MessageUpdate = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新指定消息
    """
    # 获取消息
    message = crud.crud_message.get(db, id=message_id)
    if not message:
        raise HTTPException(status_code=404, detail="消息未找到")
    
    # 验证权限
    if message.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权修改此消息")
    
    # 更新消息
    updated_message = crud.crud_message.update(db, db_obj=message, obj_in=message_update)
    
    return updated_message

# ============== WebSocket实现 ==============

@router.websocket("/stream/{session_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    session_id: str,
    db: Session = Depends(deps.get_db_websocket),
    current_user: models.User = Depends(deps.get_current_user_websocket),
):
    """
    WebSocket端点，用于流式传输AI助手的响应
    """
    # 跟踪连接状态和任务
    active_task = None
    heartbeat_task = None
    
    try:
        # 接受WebSocket连接
        await websocket.accept()
        
        # 获取或创建会话
        conversation, is_new = crud.crud_conversation.get_or_create_by_session_id(
            db, session_id=session_id, user_id=current_user.id
        )
        
        # 通知客户端连接成功
        await websocket.send_json({
            "event": "connected",
            "session_id": session_id,
            "is_new_conversation": is_new
        })
        
        # 创建心跳任务，每30秒发送一次心跳
        async def send_heartbeats():
            while True:
                try:
                    await asyncio.sleep(30)
                    await websocket.send_json({
                        "event": "heartbeat",
                        "time": time.time()
                    })
                except Exception:
                    break
        
        # 启动心跳任务
        heartbeat_task = asyncio.create_task(send_heartbeats())
        
        # 准备用户信息
        user_info = {
            "user_id": str(current_user.id),
            "nickname": current_user.nickname,
            "age": current_user.age,
            "gender": current_user.gender,
            "height": current_user.height,
            "weight": current_user.weight,
            "fitness_goal": current_user.fitness_goal,
            "experience_level": current_user.experience_level,
            "activity_level": current_user.activity_level
        }
        
        # 主消息处理循环
        while True:
            # 接收客户端消息
            data = await websocket.receive_json()
            
            # 取消之前的任务（如果有）
            if active_task and not active_task.done():
                active_task.cancel()
                try:
                    await active_task
                except asyncio.CancelledError:
                    pass
            
            # 验证消息格式
            if "message" not in data:
                await websocket.send_json({
                    "event": "error",
                    "data": {"message": "无效的消息格式，缺少message字段"}
                })
                continue
            
            # 创建处理消息的任务
            async def process_message(user_message: str, meta_info: Dict[str, Any] = None):
                try:
                    # 保存用户消息
                    user_msg = await create_user_message(
                        db,
                        conversation.id,
                        current_user.id,
                        user_message,
                        meta_info
                    )
                    
                    # 通知客户端消息已保存
                    await websocket.send_json({
                        "event": "message_saved",
                        "message_id": user_msg.id
                    })
                    
                    # 使用新版AI助手处理消息
                    complete_response = ""
                    async for chunk in conversation_orchestrator.process_message_stream(
                        user_input=user_message,
                        conversation_id=session_id,
                        user_id=str(current_user.id),
                        meta_info=user_info
                    ):
                        # 处理流式响应
                        if isinstance(chunk, dict):
                            # 如果是元数据更新，发送特殊事件
                            if chunk.get("type") == "meta_info_update":
                                await websocket.send_json({
                                    "event": "meta_info_update",
                                    "data": chunk.get("meta_info_update", {})
                                })
                            # 如果是消息类型，发送消息内容
                            elif chunk.get("type") == "message":
                                complete_response = chunk.get("content", "")
                                await websocket.send_json({
                                    "event": "message",
                                    "data": {
                                        "content": complete_response,
                                        "meta_info": chunk.get("meta_info", {})
                                    }
                                })
                            # 如果是token类型，发送文本片段
                            elif chunk.get("type") == "token":
                                token_content = chunk.get("content", "")
                                complete_response += token_content
                                await websocket.send_json({
                                    "event": "chunk",
                                    "data": {"text": token_content}
                                })
                            # 其他类型的事件
                            else:
                                await websocket.send_json({
                                    "event": chunk.get("type", "data"),
                                    "data": chunk
                                })
                        else:
                            # 文本响应片段
                            complete_response += str(chunk)
                            await websocket.send_json({
                                "event": "chunk",
                                "data": {"text": str(chunk)}
                            })
                    
                    # 保存完整的AI回复
                    if complete_response:
                        await create_assistant_message(
                            db,
                            conversation.id,
                            current_user.id,
                            complete_response,
                            {"stream_response": True}
                        )
                    
                    # 消息处理完成，发送完成事件
                    await websocket.send_json({
                        "event": "done"
                    })
                    
                except Exception as e:
                    logger.error(f"处理WebSocket消息时出错: {str(e)}", exc_info=True)
                    await websocket.send_json({
                        "event": "error",
                        "data": {"message": f"处理消息时出错: {str(e)}"}
                    })
            
            # 启动消息处理任务
            message = data["message"]
            meta_info = data.get("meta_info", {})
            active_task = asyncio.create_task(process_message(message, meta_info))
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {session_id}")
    except Exception as e:
        logger.error(f"WebSocket处理错误: {str(e)}", exc_info=True)
    finally:
        # 清理资源
        if heartbeat_task and not heartbeat_task.done():
            heartbeat_task.cancel()
        if active_task and not active_task.done():
            active_task.cancel()

@router.websocket("/stream/{session_id}/connect")
async def websocket_connect_endpoint(
    websocket: WebSocket,
    session_id: str,
    db: Session = Depends(deps.get_db_websocket),
    current_user: models.User = Depends(deps.get_current_user_websocket),
):
    """
    WebSocket连接端点（兼容性）
    """
    await websocket_endpoint(websocket, session_id, db, current_user)

# ============== 训练计划相关接口 ==============

@router.post("/generate_training_plan", response_model=ChatResponse)
async def generate_training_plan(
    *,
    db: Session = Depends(deps.get_db),
    plan_request: TrainingPlanRequest = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    生成训练计划
    """
    try:
        # 验证会话
        conversation = crud.crud_conversation.get_by_session_id(db, session_id=plan_request.session_id)
        if not conversation or conversation.user_id != current_user.id:
            raise HTTPException(status_code=404, detail="会话未找到")

        # 构建训练计划请求消息
        plan_message = f"请为我生成一个{plan_request.plan_type}训练计划"
        if plan_request.body_part:
            plan_message += f"，重点训练{plan_request.body_part}"
        if plan_request.training_scene:
            plan_message += f"，训练场景是{plan_request.training_scene}"
        if plan_request.available_time:
            plan_message += f"，可用时间{plan_request.available_time}分钟"
        if plan_request.additional_notes:
            plan_message += f"。额外要求：{plan_request.additional_notes}"

        # 保存用户消息
        user_message = await create_user_message(
            db,
            conversation.id,
            current_user.id,
            plan_message,
            {
                "training_plan_request": True,
                "plan_type": plan_request.plan_type,
                "body_part": plan_request.body_part,
                "training_scene": plan_request.training_scene
            }
        )

        # 准备用户信息
        user_info = {
            "user_id": str(current_user.id),
            "nickname": current_user.nickname,
            "age": current_user.age,
            "gender": current_user.gender,
            "height": current_user.height,
            "weight": current_user.weight,
            "fitness_goal": current_user.fitness_goal,
            "experience_level": current_user.experience_level,
            "activity_level": current_user.activity_level
        }

        # 使用AI助手生成训练计划
        response = await conversation_orchestrator.process_message(
            message=plan_message,
            conversation_id=plan_request.session_id,
            user_info=user_info
        )

        # 提取响应内容
        response_content = response.get("response_content", response.get("response", ""))

        # 保存AI回复
        if response_content:
            await create_assistant_message(
                db,
                conversation.id,
                current_user.id,
                response_content,
                {
                    "training_plan_response": True,
                    "intent": response.get("intent"),
                    "confidence": response.get("confidence")
                }
            )

        return ChatResponse(
            response=response_content,
            conversation_id=plan_request.session_id,
            session_id=plan_request.session_id,
            intent_type=response.get("intent"),
            confidence=response.get("confidence"),
            success=True,
            meta_info=response
        )

    except Exception as e:
        logger.error(f"生成训练计划时出错: {str(e)}", exc_info=True)
        return ChatResponse(
            response="抱歉，生成训练计划时出现了错误。",
            conversation_id=plan_request.session_id,
            session_id=plan_request.session_id,
            success=False,
            error=str(e)
        )

# ============== 兼容性接口 ==============

@router.post("/stream/{session_id}/message", response_model=schemas.Message)
async def send_stream_message(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    request_data: dict = Body(None),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    发送流式消息（兼容性接口）
    """
    try:
        # 验证会话
        conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
        if not conversation or conversation.user_id != current_user.id:
            raise HTTPException(status_code=404, detail="会话未找到")

        # 提取消息内容
        message_content = request_data.get("message", "")
        if not message_content:
            raise HTTPException(status_code=400, detail="消息内容不能为空")

        # 保存用户消息
        user_message = await create_user_message(
            db,
            conversation.id,
            current_user.id,
            message_content,
            request_data.get("meta_info", {})
        )

        return user_message

    except Exception as e:
        logger.error(f"发送流式消息时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"发送消息失败: {str(e)}")

@router.get("/stream/{session_id}/connect", response_model=schemas.BasicResponse)
def wechat_websocket_connect(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    token: Optional[str] = Query(None, description="认证Token"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    微信WebSocket连接信息（兼容性接口）
    """
    # 验证会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话未找到")

    return {
        "status": "success",
        "message": "WebSocket连接信息",
        "data": {
            "session_id": session_id,
            "websocket_url": f"/api/v2/chat/stream/{session_id}",
            "user_id": current_user.id
        }
    }

@router.get("/stream/{session_id}", response_model=schemas.BasicResponse)
def websocket_stream_info(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取WebSocket流信息（兼容性接口）
    """
    # 验证会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话未找到")

    return {
        "status": "success",
        "message": "WebSocket流信息",
        "data": {
            "session_id": session_id,
            "status": "active",
            "websocket_url": f"/api/v2/chat/stream/{session_id}",
            "last_active": conversation.last_active_at
        }
    }
 
 